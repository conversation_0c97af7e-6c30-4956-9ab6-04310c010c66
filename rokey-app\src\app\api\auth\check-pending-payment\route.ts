import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if user exists with pending payment status
    const { data: users, error: queryError } = await supabase.auth.admin.listUsers();

    if (queryError) {
      console.error('Error querying users:', queryError);
      return NextResponse.json(
        { error: 'Failed to check user status' },
        { status: 500 }
      );
    }

    // Find user by email
    const user = users.users.find(u => u.email === email);

    if (!user) {
      return NextResponse.json({
        exists: false,
        message: 'No account found with this email address.'
      });
    }

    // Check if user has pending payment status
    const paymentStatus = user.user_metadata?.payment_status;
    const userPlan = user.user_metadata?.plan;

    if (paymentStatus === 'pending' && userPlan && ['starter', 'professional', 'enterprise'].includes(userPlan)) {
      return NextResponse.json({
        exists: true,
        hasPendingPayment: true,
        plan: userPlan,
        message: `You have a pending payment for your ${userPlan} plan. Please sign in to complete your checkout.`,
        signInUrl: `/auth/signin?message=complete_payment&plan=${userPlan}&email=${encodeURIComponent(email)}`
      });
    }

    // Check if user has active subscription
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('subscription_tier, subscription_status')
      .eq('id', user.id)
      .single();

    if (profile && profile.subscription_status === 'active') {
      return NextResponse.json({
        exists: true,
        hasPendingPayment: false,
        hasActiveSubscription: true,
        tier: profile.subscription_tier,
        message: `You already have an active ${profile.subscription_tier} account. Please sign in to access your dashboard.`,
        signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`
      });
    }

    // User exists but no clear status
    return NextResponse.json({
      exists: true,
      hasPendingPayment: false,
      hasActiveSubscription: false,
      message: 'Account found. Please sign in to continue.',
      signInUrl: `/auth/signin?email=${encodeURIComponent(email)}`
    });

  } catch (error) {
    console.error('Error checking pending payment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'Pending payment check endpoint',
    usage: 'POST /api/auth/check-pending-payment with { email: "<EMAIL>" }'
  });
}
