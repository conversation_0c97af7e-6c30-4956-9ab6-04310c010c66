'use client';

import { useState, useEffect, use<PERSON><PERSON>back, ChangeEvent, FormEvent } from 'react';
import {
  AdjustmentsHorizontalIcon, CalendarDaysIcon, DocumentMagnifyingGlassIcon,
  EyeIcon, ArrowPathIcon, ArrowUpIcon, ArrowDownIcon, ArrowsUpDownIcon,
  FunnelIcon, ClockIcon, DocumentTextIcon
} from '@heroicons/react/24/outline';
import LogDetailModal from '@/components/logs/LogDetailModal';
import Button from '@/components/ui/Button';
import Card, { CardHeader, CardContent } from '@/components/ui/Card';
import Input, { Select } from '@/components/ui/Input';
import { LoadingTable } from '@/components/ui/LoadingSpinner';
import { transformRoleUsed, getRoleUsedBadgeClass, formatProviderName, formatModelName } from '@/utils/logFormatting';
import { useSubscription } from '@/hooks/useSubscription';

// Types from MyModelsPage - assuming they might be moved to a shared types file later
interface CustomApiConfigMini {
  id: string;
  name: string;
}

// Define types for Log entry and Pagination
interface RequestLog {
  id: string;
  request_timestamp: string;
  custom_api_config_id: string | null;
  custom_api_config_name?: string; // Will be populated client-side after fetching configs
  role_requested: string | null;
  role_used: string | null;
  llm_provider_name: string | null;
  llm_model_name: string | null;
  status_code: number | null;
  llm_provider_latency_ms: number | null;
  processing_duration_ms: number | null;
  input_tokens: number | null;
  output_tokens: number | null;
  cost: number | null;
  is_multimodal: boolean | null;
  ip_address: string | null;
  user_id: string | null;
  error_message: string | null;
  error_source: string | null;
  error_details_zod: string | null;
  llm_provider_status_code: number | null;
  request_payload_summary: any | null;
  response_payload_summary: any | null;
}

interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

interface FiltersState {
  startDate: string;
  endDate: string;
  customApiConfigId: string;
  status: string;
}

interface SortState {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Define which columns are sortable and map display name to field name
const sortableColumns: { label: string; field: string; defaultSortOrder?: 'asc' | 'desc' }[] = [
  { label: 'Timestamp', field: 'request_timestamp', defaultSortOrder: 'desc' },
  { label: 'API Model', field: 'custom_api_config_id' }, // Sorting by name would require join or client-side sort on resolved names
  { label: 'Role Used', field: 'role_used' },
  { label: 'Provider', field: 'llm_provider_name' },
  { label: 'LLM Model', field: 'llm_model_name' },
  { label: 'Status', field: 'status_code' },
  { label: 'Latency (LLM)', field: 'llm_provider_latency_ms' },
  { label: 'Latency (RoKey)', field: 'processing_duration_ms' },
  { label: 'Input Tokens', field: 'input_tokens' },
  { label: 'Output Tokens', field: 'output_tokens' },
];

const DEFAULT_PAGE_SIZE = 10; // Reduced for faster initial load

export default function LogsPage() {
  const { user } = useSubscription();
  const [logs, setLogs] = useState<RequestLog[]>([]);
  const [pagination, setPagination] = useState<PaginationState | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingConfigs, setIsLoadingConfigs] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [apiConfigs, setApiConfigs] = useState<CustomApiConfigMini[]>([]);

  const initialFilters: FiltersState = {
    startDate: '',
    endDate: '',
    customApiConfigId: 'all',
    status: 'all',
  };
  const [filters, setFilters] = useState<FiltersState>(initialFilters);
  const [showFilters, setShowFilters] = useState<boolean>(false);

  const [customConfigNameMap, setCustomConfigNameMap] = useState<Record<string, string>>({});

  // State for Log Detail Modal
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedLog, setSelectedLog] = useState<RequestLog | null>(null);

  const [sort, setSort] = useState<SortState>({ sortBy: 'request_timestamp', sortOrder: 'desc' });

  const fetchApiConfigs = async () => {
    setIsLoadingConfigs(true);
    try {
      const response = await fetch('/api/custom-configs');
      if (!response.ok) {
        throw new Error('Failed to fetch API model configurations');
      }
      const data: CustomApiConfigMini[] = await response.json();
      setApiConfigs(data);
      const nameMap: Record<string, string> = {};
      data.forEach(config => { nameMap[config.id] = config.name; });
      setCustomConfigNameMap(nameMap);
    } catch (err: any) {
      setError(`Error fetching configurations: ${err.message}`);
    } finally {
      setIsLoadingConfigs(false);
    }
  };

  const fetchLogs = useCallback(async (page = 1, currentFilters: FiltersState, currentSort: SortState) => {
    setIsLoading(true);
    setError(null);
    try {
      const params: Record<string, string> = {
        page: page.toString(),
        pageSize: DEFAULT_PAGE_SIZE.toString(),
        sortBy: currentSort.sortBy,
        sortOrder: currentSort.sortOrder,
      };
      if (currentFilters.startDate) params.startDate = new Date(currentFilters.startDate).toISOString();
      if (currentFilters.endDate) params.endDate = new Date(currentFilters.endDate).toISOString();
      if (currentFilters.customApiConfigId !== 'all') params.customApiConfigId = currentFilters.customApiConfigId;
      if (currentFilters.status !== 'all') params.status = currentFilters.status;
      
      const response = await fetch(`/api/logs?${new URLSearchParams(params).toString()}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.details || 'Failed to fetch logs');
      }
      const data = await response.json();
      setLogs(data.logs || []);
      setPagination(data.pagination || null);
    } catch (err: any) {
      setError(err.message);
      setLogs([]);
      setPagination(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Only fetch data when user is authenticated
    if (user) {
      // Delay initial data fetching to improve perceived performance
      const timer = setTimeout(() => {
        fetchApiConfigs();
        fetchLogs(1, filters, sort);
      }, 100);

      return () => clearTimeout(timer);
    } else if (user === null) {
      // User is explicitly null (not authenticated), stop loading
      setIsLoading(false);
      setIsLoadingConfigs(false);
    }
    // If user is undefined, we're still loading auth state, keep loading
  }, [fetchLogs, filters, sort, user]); // Add user dependency

  const handleFilterChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFilters(prev => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleApplyFilters = (e?: FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    fetchLogs(1, filters, sort);
  };

  const handleResetFilters = () => {
    setFilters(initialFilters);
    const defaultSort = { sortBy: 'request_timestamp', sortOrder: 'desc' } as SortState;
    setSort(defaultSort);
    fetchLogs(1, initialFilters, defaultSort);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && (!pagination || newPage <= pagination.totalPages)) {
      fetchLogs(newPage, filters, sort);
    }
  };

  const handleSort = (field: string) => {
    const newSortOrder = (sort.sortBy === field && sort.sortOrder === 'asc') ? 'desc' : 'asc';
    const newSortState: SortState = { sortBy: field, sortOrder: newSortOrder };
    setSort(newSortState);
    fetchLogs(1, filters, newSortState);
  };

  // Handlers for Log Detail Modal
  const handleOpenModal = (log: RequestLog) => {
    setSelectedLog(log);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedLog(null);
  };

  const getStatusClass = (statusCode: number | null) => {
    if (statusCode === null) return 'bg-gray-600 text-gray-100';
    if (statusCode >= 200 && statusCode < 300) return 'bg-green-600 text-green-100';
    if (statusCode >= 400) return 'bg-red-600 text-red-100';
    return 'bg-yellow-500 text-yellow-100'; // For 3xx or other statuses
  };

  const SortableHeader: React.FC<{ column: typeof sortableColumns[0] }> = ({ column }) => {
    const isCurrentSortColumn = sort.sortBy === column.field;
    return (
      <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
        <button
          onClick={() => handleSort(column.field)}
          className="flex items-center space-x-2 hover:text-gray-900 transition-colors duration-200 group"
        >
          <span>{column.label}</span>
          {isCurrentSortColumn ? (
            sort.sortOrder === 'asc' ?
              <ArrowUpIcon className="h-4 w-4 text-orange-500" /> :
              <ArrowDownIcon className="h-4 w-4 text-orange-500" />
          ) : (
            <ArrowsUpDownIcon className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
          )}
        </button>
      </th>
    );
  };

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-4xl font-bold text-gray-900">
            📊 Request Logs
          </h1>
          <p className="text-gray-600 mt-2">
            Monitor and analyze your API request history
          </p>
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={showFilters ? "btn-primary" : "btn-secondary"}
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="card border-red-200 bg-red-50 p-4">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="card p-6 animate-scale-in">
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">Filter Logs</h3>
                <p className="text-gray-600 mt-1">Narrow down your search results</p>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <ClockIcon className="h-4 w-4" />
                <span>Real-time updates</span>
              </div>
            </div>
          </div>
          <form onSubmit={handleApplyFilters} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <CalendarDaysIcon className="h-4 w-4 inline mr-1" />
                  Start Date
                </label>
                <input
                  type="date"
                  name="startDate"
                  value={filters.startDate}
                  onChange={handleFilterChange}
                  className="form-input"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <CalendarDaysIcon className="h-4 w-4 inline mr-1" />
                  End Date
                </label>
                <input
                  type="date"
                  name="endDate"
                  value={filters.endDate}
                  onChange={handleFilterChange}
                  className="form-input"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  API Model
                </label>
                <select
                  name="customApiConfigId"
                  value={filters.customApiConfigId}
                  onChange={handleFilterChange}
                  disabled={isLoadingConfigs}
                  className="form-select"
                >
                  <option value="all">All Models</option>
                  {apiConfigs.map(config => (
                    <option key={config.id} value={config.id}>{config.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  name="status"
                  value={filters.status}
                  onChange={handleFilterChange}
                  className="form-select"
                >
                  <option value="all">All Statuses</option>
                  <option value="success">Success</option>
                  <option value="error">Error</option>
                </select>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                type="submit"
                className="btn-primary flex-1"
              >
                <DocumentMagnifyingGlassIcon className="h-4 w-4 mr-2" />
                Apply Filters
              </button>
              <button
                type="button"
                onClick={handleResetFilters}
                className="btn-secondary flex-1"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                Reset Filters
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Loading State */}
      {isLoading && <LoadingTable rows={8} columns={11} />}

      {/* Empty State */}
      {!isLoading && !logs.length && !error && (
        <div className="card text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100">
              <DocumentTextIcon className="h-8 w-8 text-orange-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No Logs Found</h3>
            <p className="text-gray-600 mb-6">
              No request logs match your criteria. Once you make requests to the unified API, they will appear here.
            </p>
          </div>
        </div>
      )}

      {/* Logs Table */}
      {!isLoading && logs.length > 0 && (
        <>
          <div className="card overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    {sortableColumns.map(col => <SortableHeader key={col.field} column={col} />)}
                    <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                      Multimodal
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {logs.map((log, index) => (
                    <tr
                      key={log.id}
                      className="hover:bg-gray-50 transition-colors duration-200 animate-slide-in"
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900">
                        <div className="flex items-center space-x-2">
                          <ClockIcon className="h-4 w-4 text-gray-500" />
                          <span>{new Date(log.request_timestamp).toLocaleString()}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-gray-900">
                        <div className="font-medium">
                          {log.custom_api_config_id ? (customConfigNameMap[log.custom_api_config_id] || log.custom_api_config_id.substring(0,8) + '...') : 'N/A'}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-gray-900">
                        {(() => {
                          const roleInfo = transformRoleUsed(log.role_used);
                          return (
                            <span className={getRoleUsedBadgeClass(roleInfo.type)}>
                              {roleInfo.text}
                            </span>
                          );
                        })()}
                      </td>
                      <td className="px-6 py-4 text-gray-900">
                        <span className="font-medium">{formatProviderName(log.llm_provider_name)}</span>
                      </td>
                      <td className="px-6 py-4 text-gray-900 truncate max-w-xs" title={formatModelName(log.llm_model_name)}>
                        <span className="font-medium">{formatModelName(log.llm_model_name)}</span>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusClass(log.status_code)}`}>
                          {log.status_code || 'N/A'}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-right text-gray-900">
                        {log.llm_provider_latency_ms !== null ? `${log.llm_provider_latency_ms} ms` : '-'}
                      </td>
                      <td className="px-6 py-4 text-right text-gray-900">
                        {log.processing_duration_ms !== null ? `${log.processing_duration_ms} ms` : '-'}
                      </td>
                      <td className="px-6 py-4 text-right text-gray-900">
                        {log.input_tokens !== null ? log.input_tokens.toLocaleString() : '-'}
                      </td>
                      <td className="px-6 py-4 text-right text-gray-900">
                        {log.output_tokens !== null ? log.output_tokens.toLocaleString() : '-'}
                      </td>
                      <td className="px-6 py-4 text-center">
                        {log.is_multimodal ? (
                          <span className="w-2 h-2 bg-green-500 rounded-full inline-block"></span>
                        ) : (
                          <span className="w-2 h-2 bg-gray-400 rounded-full inline-block"></span>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <button
                          onClick={() => handleOpenModal(log)}
                          className="p-2 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination Controls */}
          {pagination && pagination.totalPages > 1 && (
            <div className="card p-4">
              <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
                <div className="text-sm text-gray-600">
                  Showing <span className="font-medium text-gray-900">{(pagination.currentPage - 1) * pagination.pageSize + 1}</span>
                  {logs.length > 0 ? ` - ${Math.min(pagination.currentPage * pagination.pageSize, pagination.totalCount)}` : ''}
                  {' '}of <span className="font-medium text-gray-900">{pagination.totalCount}</span> logs
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage <= 1 || isLoading}
                    className="btn-secondary text-sm px-3 py-1"
                  >
                    Previous
                  </button>
                  <span className="px-3 py-1 text-sm text-gray-600">
                    Page {pagination.currentPage} of {pagination.totalPages}
                  </span>
                  <button
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage >= pagination.totalPages || isLoading}
                    className="btn-secondary text-sm px-3 py-1"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      )}
      
      {/* TODO: Log Detail Modal will be managed here */}
      {isModalOpen && selectedLog && (
        <LogDetailModal 
          log={selectedLog} 
          onClose={handleCloseModal} 
          apiConfigNameMap={customConfigNameMap} 
        />
      )}
    </div>
  );
} 