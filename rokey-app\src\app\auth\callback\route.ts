import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // IMMEDIATE LOG TO VERIFY ROUTE IS HIT
  console.log('🔥🔥🔥 AUTH CALLBACK ROUTE HIT 🔥🔥🔥');

  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const redirectTo = requestUrl.searchParams.get('redirectTo');

  // SERVER LOG: Auth callback started
  console.log('🔥 AUTH CALLBACK STARTED');
  console.log('🔥 Full URL:', request.url);
  console.log('🔥 Code present:', !!code);
  console.log('🔥 RedirectTo:', redirectTo);

  if (code) {
    const supabase = createSupabaseServerClientFromRequest(request);

    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.log('🔥 ERROR: Failed to exchange code for session:', error);
        return NextResponse.redirect(new URL('/auth/signin?error=auth_callback_error', request.url));
      }

      console.log('🔥 SUCCESS: Session exchanged for user:', data.user?.id);
      console.log('🔥 User email:', data.user?.email);
      console.log('🔥 User created at:', data.user?.created_at);
      console.log('🔥 User metadata:', data.user?.user_metadata);

      // If this is a redirect to checkout, go there immediately
      if (redirectTo && redirectTo.includes('/checkout')) {
        console.log('🔥 DIRECT CHECKOUT REDIRECT:', redirectTo);
        return NextResponse.redirect(new URL(redirectTo, request.url));
      }

      // If this is after payment, check subscription status and redirect to dashboard
      if (requestUrl.searchParams.get('payment_success') === 'true') {
        // Check if user has completed payment
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_status, subscription_tier')
          .eq('id', data.user.id)
          .single();

        if (profile && profile.subscription_status === 'active') {
          // Payment completed, redirect to dashboard
          return NextResponse.redirect(new URL('/dashboard', request.url));
        } else {
          // Payment processing, wait a moment and redirect to dashboard anyway
          // The webhook will update the status shortly
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      }

      // Since we removed Google OAuth, this callback is mainly for future use
      // For now, just redirect to the specified location or dashboard
      if (data.user) {

        // Simple redirect to specified location or dashboard
        const finalRedirect = redirectTo || '/dashboard';
        console.log('🔥 REDIRECTING TO:', finalRedirect);
        return NextResponse.redirect(new URL(finalRedirect, request.url));
      }

    } catch (error) {
      console.error('Error exchanging code for session:', error);
      return NextResponse.redirect(new URL('/auth/signin?error=auth_callback_error', request.url));
    }
  }

  // Default redirect
  const finalRedirect = redirectTo || '/dashboard';
  console.log('🔥 DEFAULT REDIRECT TO:', finalRedirect);
  return NextResponse.redirect(new URL(finalRedirect, request.url));
}
