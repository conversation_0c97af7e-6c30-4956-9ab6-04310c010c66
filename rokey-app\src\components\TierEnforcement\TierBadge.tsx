'use client';

import React from 'react';
import { SubscriptionTier } from '@/lib/stripe-client';
import { StarIcon, TrophyIcon, ShieldCheckIcon, SparklesIcon } from '@heroicons/react/24/solid';

interface TierBadgeProps {
  tier: SubscriptionTier;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

const tierConfig = {
  free: {
    name: 'Free',
    color: 'bg-gray-100 text-gray-800 border-gray-300',
    icon: ShieldCheckIcon,
    iconColor: 'text-gray-600'
  },
  starter: {
    name: 'Starter',
    color: 'bg-blue-100 text-blue-800 border-blue-300',
    icon: StarIcon,
    iconColor: 'text-blue-600'
  },
  professional: {
    name: 'Professional',
    color: 'bg-orange-100 text-orange-800 border-orange-300',
    icon: SparklesIcon,
    iconColor: 'text-orange-600'
  },
  enterprise: {
    name: 'Enterprise',
    color: 'bg-purple-100 text-purple-800 border-purple-300',
    icon: TrophyIcon,
    iconColor: 'text-purple-600'
  }
};

const sizeConfig = {
  sm: {
    container: 'px-2 py-1 text-xs',
    icon: 'w-3 h-3'
  },
  md: {
    container: 'px-3 py-1 text-sm',
    icon: 'w-4 h-4'
  },
  lg: {
    container: 'px-4 py-2 text-base',
    icon: 'w-5 h-5'
  }
};

export function TierBadge({ 
  tier, 
  size = 'md', 
  showIcon = true, 
  className = '' 
}: TierBadgeProps) {
  const config = tierConfig[tier];
  const sizeStyles = sizeConfig[size];
  const Icon = config.icon;

  return (
    <span className={`
      inline-flex items-center space-x-1 font-medium border rounded-full
      ${config.color} 
      ${sizeStyles.container}
      ${className}
    `}>
      {showIcon && (
        <Icon className={`${sizeStyles.icon} ${config.iconColor}`} />
      )}
      <span>{config.name}</span>
    </span>
  );
}
