'use client';

import { Suspense, useState, useEffect } from "react";
import Navbar from "@/components/Navbar";
import Sidebar from "@/components/Sidebar";
import { useSidebar } from "@/contexts/SidebarContext";
import { useNavigationSafe } from "@/contexts/NavigationContext";
import OptimisticLoadingScreen from "@/components/OptimisticLoadingScreen";
import OptimisticPageLoader from "@/components/OptimisticPageLoader";
import { useAdvancedPreloading } from "@/hooks/useAdvancedPreloading";
import { useToast, ToastContainer } from "@/components/ui/Toast";

function LayoutContentInner({ children }: { children: React.ReactNode }) {
  const { isCollapsed, isHovered, collapseSidebar } = useSidebar();
  const navigationContext = useNavigationSafe();
  const { isNavigating, targetRoute, isPageCached } = navigationContext || {
    isNavigating: false,
    targetRoute: null,
    isPageCached: () => false
  };

  // Toast notifications
  const { toasts, removeToast } = useToast();

  // Track if we're on desktop (lg breakpoint and above)
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024); // lg breakpoint
    };

    // Check on mount
    checkIsDesktop();

    // Listen for resize events
    window.addEventListener('resize', checkIsDesktop);
    return () => window.removeEventListener('resize', checkIsDesktop);
  }, []);

  // Calculate actual sidebar width based on collapsed and hover states
  // Only apply sidebar width on desktop, mobile uses overlay
  const sidebarWidth = isDesktop ? ((!isCollapsed || isHovered) ? 256 : 64) : 0;

  // Initialize advanced preloading system
  useAdvancedPreloading({
    maxConcurrent: 2,
    idleTimeout: 1500,
    backgroundDelay: 3000
  });

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Desktop Sidebar - Fixed Position */}
      <div className="hidden lg:block fixed left-0 top-0 h-full z-40">
        <Sidebar />
      </div>

      {/* Mobile Sidebar Overlay */}
      <div className={`lg:hidden fixed inset-0 z-50 ${isCollapsed ? 'pointer-events-none' : ''}`}>
        {/* Backdrop */}
        <div
          onClick={collapseSidebar}
          className={`absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ${
            isCollapsed ? 'opacity-0' : 'opacity-50'
          }`}
        />

        {/* Sidebar */}
        <div className={`absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ${
          isCollapsed ? '-translate-x-full' : 'translate-x-0'
        }`}>
          <Sidebar />
        </div>
      </div>

      {/* Main content area - Adjusted for fixed sidebar */}
      <div
        className="flex-1 flex flex-col overflow-hidden min-w-0 transition-all duration-200 ease-out"
        style={{
          marginLeft: `${sidebarWidth}px`
        }}
      >
        {/* Fixed Top Navigation */}
        <div
          className="fixed top-0 right-0 z-30 transition-all duration-200 ease-out"
          style={{ left: `${sidebarWidth}px` }}
        >
          <Navbar />
        </div>

        {/* Main scrollable content with top margin for fixed navbar */}
        <main className="flex-1 overflow-y-auto content-area mt-16">
          <div className={`p-4 sm:p-6 lg:p-8 w-full ${
            // When sidebar is expanded, use standard max width with centering
            // When sidebar is collapsed, use larger max width or no max width
            isDesktop && (!isCollapsed || isHovered)
              ? 'max-w-7xl mx-auto'
              : isDesktop
                ? 'max-w-none px-8'
                : 'max-w-7xl mx-auto'
          }`}>
            <div className="page-transition">
              {isNavigating && targetRoute ? (
                <OptimisticPageLoader targetRoute={targetRoute}>
                  {children}
                </OptimisticPageLoader>
              ) : (
                children
              )}
            </div>
          </div>
        </main>
      </div>

      {/* Toast Container */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}

export default function LayoutContent({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={<OptimisticLoadingScreen targetRoute={null} />}>
      <LayoutContentInner>{children}</LayoutContentInner>
    </Suspense>
  );
}
