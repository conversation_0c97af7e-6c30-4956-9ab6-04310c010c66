'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  BoltIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';

const stats = [
  { label: "AI Models Supported", value: "300+" },
  { label: "API Requests Processed", value: "10M+" },
  { label: "Developers Trust Us", value: "5,000+" },
  { label: "Uptime Guarantee", value: "99.9%" }
];

const values = [
  {
    icon: BoltIcon,
    title: "Performance First",
    description: "We obsess over speed, reliability, and efficiency in everything we build."
  },
  {
    icon: ShieldCheckIcon,
    title: "Security by Design",
    description: "Enterprise-grade security isn&apos;t an afterthought—it&apos;s built into our foundation."
  },
  {
    icon: LightBulbIcon,
    title: "Innovation Driven",
    description: "We&apos;re constantly pushing the boundaries of what&apos;s possible with AI routing."
  },
  {
    icon: UserGroupIcon,
    title: "Solo Built",
    description: "Built entirely by one developer who faced these exact problems and solved them."
  }
];

const founder = {
  name: "<PERSON><PERSON>kwunyerem",
  role: "Founder & Developer",
  bio: "Game developer and app developer who built RouKey to solve the frustrations of managing multiple AI providers and routing systems.",
  image: "/api/placeholder/150/150"
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                From $500 API Bills to
                <span className="text-[#ff6b35] block">Unlimited AI Access</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                The inspiring story of how one developer&apos;s frustration with expensive AI routing led to building RouKey - the world&apos;s most intelligent AI gateway with unlimited requests to 300+ models, completely solo.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="text-center"
                >
                  <div className="text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-6">The Breaking Point</h2>
              <p className="text-xl text-gray-600">
                Every developer has that moment when they decide to build the tool they wish existed. This is mine.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="prose prose-lg mx-auto text-gray-700"
            >
              <p>
                <strong>It was 2 AM.</strong> I was deep into developing a game that heavily relied on AI for procedural content generation.
                My API bills were skyrocketing, I&apos;d hit rate limits on three different providers, and the &quot;intelligent&quot;
                routing tool I was paying for had just routed my simple chat request to GPT-4 Turbo for the hundredth time that day.
              </p>
              <p>
                <strong>That&apos;s when it hit me.</strong> I&apos;m Okoro David Chukwunyerem, and I&apos;ve been building games and apps for years.
                I know how to solve complex routing problems. Why was I trusting someone else&apos;s broken solution when I could
                build something that actually works?
              </p>
              <p>
                <strong>The eureka moment:</strong> While experimenting with different providers, I discovered that by intelligently
                routing between multiple free trial API keys, I could get essentially unlimited usage for testing. No more
                $500 monthly bills for development work. No more hitting rate limits mid-sprint.
              </p>
              <p>
                <strong>Six months later,</strong> what started as a weekend hack to solve my own problems had evolved into RouKey -
                a platform that gives any developer unlimited access to 300+ AI models with truly intelligent routing.
                Built entirely solo, tested in the trenches of real development work.
              </p>
              <p className="text-[#ff6b35] font-semibold text-lg border-l-4 border-[#ff6b35] pl-6 italic">
                &quot;I built RouKey because I was tired of choosing between going broke or building slowly.
                Now thousands of developers can test fearlessly and build faster than ever before.&quot;
              </p>
            </motion.div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-6">My Values</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                These principles guide how I built RouKey and how I continue to develop it.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              {values.map((value, index) => (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="flex items-start space-x-4"
                >
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center">
                      <value.icon className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      {value.title}
                    </h3>
                    <p className="text-gray-600">
                      {value.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Founder Section - Enhanced Layout */}
        <section className="py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute inset-0 opacity-5">
            <div
              className="absolute inset-0"
              style={{
                backgroundImage: `
                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                `,
                backgroundSize: '60px 60px'
              }}
            ></div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="text-center mb-20"
            >
              <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                Meet the
                <span className="text-[#ff6b35] block">Solo Founder</span>
              </h2>
              <p className="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                One developer&apos;s journey from frustration to building the ultimate AI routing platform
              </p>
            </motion.div>

            {/* Large founder card with side-by-side layout */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="bg-white rounded-3xl shadow-2xl overflow-hidden max-w-6xl mx-auto"
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                {/* Image Side */}
                <div className="relative bg-gradient-to-br from-[#ff6b35] to-[#f7931e] p-8 flex items-center justify-center min-h-[700px] group">
                  <div className="relative">
                    <div className="w-[500px] h-[500px] rounded-3xl overflow-hidden shadow-2xl border-4 border-white/20 group-hover:scale-105 transition-transform duration-500">
                      <img
                        src="/founder.jpg"
                        alt="Okoro David Chukwunyerem - Founder and Solo Developer of RouKey AI Gateway"
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        loading="lazy"
                      />
                    </div>
                    {/* Floating achievement badges */}
                    <div className="absolute -top-6 -left-6 bg-white rounded-2xl px-4 py-2 shadow-xl transform -rotate-12 hover:rotate-0 transition-transform duration-300">
                      <div className="text-[#ff6b35] font-bold text-sm">Solo Built</div>
                    </div>
                    <div className="absolute -bottom-6 -right-6 bg-white rounded-2xl px-4 py-2 shadow-xl transform rotate-12 hover:rotate-0 transition-transform duration-300">
                      <div className="text-[#ff6b35] font-bold text-sm">300+ Models</div>
                    </div>
                    <div className="absolute top-1/2 -right-8 bg-white rounded-2xl px-4 py-2 shadow-xl transform rotate-6 hover:rotate-0 transition-transform duration-300">
                      <div className="text-[#ff6b35] font-bold text-sm">Game Dev</div>
                    </div>
                    <div className="absolute bottom-1/4 -left-8 bg-white rounded-2xl px-4 py-2 shadow-xl transform -rotate-6 hover:rotate-0 transition-transform duration-300">
                      <div className="text-[#ff6b35] font-bold text-sm">∞ Requests</div>
                    </div>
                  </div>
                </div>

                {/* Content Side */}
                <div className="p-12 flex flex-col justify-center">
                  <div className="mb-8">
                    <h3 className="text-4xl font-bold text-gray-900 mb-3">
                      {founder.name}
                    </h3>
                    <p className="text-2xl text-[#ff6b35] font-semibold mb-6">
                      {founder.role}
                    </p>
                    <div className="w-20 h-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full mb-8"></div>
                  </div>

                  <div className="space-y-6 text-lg text-gray-700 leading-relaxed">
                    <p>
                      <strong className="text-gray-900">Game developer turned AI infrastructure pioneer.</strong>
                      After countless nights wrestling with broken routing tools and mounting API bills,
                      I decided to build the solution I wished existed.
                    </p>
                    <p>
                      <strong className="text-gray-900">The breakthrough:</strong> While developing games that heavily used AI,
                      I discovered that intelligent routing between multiple free trial API keys could give me
                      essentially unlimited testing access. No more $500 monthly bills for development work.
                    </p>
                    <div className="bg-gray-50 rounded-2xl p-6 border-l-4 border-[#ff6b35]">
                      <p className="text-[#ff6b35] font-semibold italic">
                        &quot;I built RouKey because I was tired of choosing between going broke or building slowly.
                        Six months later, thousands of developers are testing fearlessly and building faster than ever.&quot;
                      </p>
                    </div>
                    <p>
                      <strong className="text-gray-900">Every line of code</strong> in RouKey was written by me.
                      Every feature was born from a real problem I faced. That&apos;s why it actually works.
                    </p>
                  </div>

                  {/* Achievement Stats */}
                  <div className="grid grid-cols-3 gap-6 mt-10 pt-8 border-t border-gray-200">
                    <div className="text-center group">
                      <div className="text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300">1</div>
                      <div className="text-gray-600 text-sm">Solo Developer</div>
                    </div>
                    <div className="text-center group">
                      <div className="text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300">300+</div>
                      <div className="text-gray-600 text-sm">AI Models</div>
                    </div>
                    <div className="text-center group">
                      <div className="text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300">∞</div>
                      <div className="text-gray-600 text-sm">API Requests</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-32 bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden">
          {/* Background effects */}
          <div className="absolute inset-0">
            <div className="absolute top-20 left-10 w-96 h-96 bg-[#ff6b35] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
            <div className="absolute bottom-20 right-10 w-96 h-96 bg-[#f7931e] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse" style={{ animationDelay: '2s' }}></div>
          </div>

          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                Ready to Build
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] block">
                  Without Limits?
                </span>
              </h2>
              <p className="text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
                Join thousands of developers who&apos;ve discovered the secret to unlimited AI testing.
                Built by a developer who faced your exact problems.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                <Link href="/auth/signup" prefetch={true}>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-5 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer"
                  >
                    Start Building Now
                  </motion.div>
                </Link>
                <Link href="/pricing" prefetch={true}>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-white/10 backdrop-blur-sm text-white px-12 py-5 rounded-2xl font-bold text-xl border-2 border-white/20 hover:border-white/40 hover:bg-white/20 transition-all duration-200 cursor-pointer"
                  >
                    View Pricing
                  </motion.div>
                </Link>
              </div>

              {/* Final stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                <div className="text-center">
                  <div className="text-4xl font-bold text-[#ff6b35] mb-2">∞</div>
                  <div className="text-gray-400">API Requests</div>
                  <div className="text-gray-500 text-sm">No limits, ever</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-[#ff6b35] mb-2">300+</div>
                  <div className="text-gray-400">AI Models</div>
                  <div className="text-gray-500 text-sm">All providers</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-[#ff6b35] mb-2">$0</div>
                  <div className="text-gray-400">Overage Fees</div>
                  <div className="text-gray-500 text-sm">Pay only your API costs</div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
