'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import {
  Copy,
  Trash2,
  Calendar,
  Activity,
  Shield,
  Globe
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';

interface ApiKeyCardProps {
  apiKey: {
    id: string;
    key_name: string;
    key_prefix: string;
    masked_key: string;
    permissions: {
      chat: boolean;
      streaming: boolean;
      all_models: boolean;
    };
    rate_limit_per_minute: number;
    rate_limit_per_hour: number;
    rate_limit_per_day: number;
    allowed_ips: string[];
    allowed_domains: string[];
    total_requests: number;
    last_used_at?: string;
    status: 'active' | 'inactive' | 'revoked' | 'expired';
    expires_at?: string;
    created_at: string;
    custom_api_configs: {
      id: string;
      name: string;
    };
  };
  onRevoke: (apiKeyId: string) => void;
}

export function ApiKeyCard({ apiKey, onRevoke }: ApiKeyCardProps) {
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('API key copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy API key');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'revoked':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const isExpired = apiKey.expires_at && new Date(apiKey.expires_at) < new Date();
  const isActive = apiKey.status === 'active' && !isExpired;

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${!isActive ? 'opacity-75' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold">{apiKey.key_name}</CardTitle>
            <p className="text-sm text-gray-600">
              Configuration: {apiKey.custom_api_configs.name}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(apiKey.status)}>
              {apiKey.status}
            </Badge>
            {isExpired && (
              <Badge className="bg-red-100 text-red-800 border-red-200">
                Expired
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* API Key Display */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">API Key (Masked)</label>
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
            <code className="flex-1 text-sm font-mono text-gray-600">
              {apiKey.key_prefix}_{'*'.repeat(28)}{typeof apiKey.masked_key === 'string' ? apiKey.masked_key.slice(-4) : 'xxxx'}
            </code>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(`${apiKey.key_prefix}_${'*'.repeat(28)}${typeof apiKey.masked_key === 'string' ? apiKey.masked_key.slice(-4) : 'xxxx'}`)}
              className="h-8 w-8 p-0"
              title="Copy masked key (for reference only)"
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center gap-2 text-xs text-amber-600 bg-amber-50 p-2 rounded">
            <span>⚠️</span>
            <span>Full API key was only shown once during creation for security. Save it securely when creating new keys.</span>
          </div>
        </div>

        {/* Permissions */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Permissions</label>
          <div className="flex flex-wrap gap-2">
            {apiKey.permissions.chat && (
              <Badge variant="secondary" className="text-xs">
                Chat Completions
              </Badge>
            )}
            {apiKey.permissions.streaming && (
              <Badge variant="secondary" className="text-xs">
                Streaming
              </Badge>
            )}
            {apiKey.permissions.all_models && (
              <Badge variant="secondary" className="text-xs">
                All Models
              </Badge>
            )}
          </div>
        </div>



        {/* Security Restrictions */}
        {(apiKey.allowed_ips.length > 0 || apiKey.allowed_domains.length > 0) && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
              <Shield className="h-4 w-4" />
              Security Restrictions
            </label>
            <div className="space-y-1 text-xs">
              {apiKey.allowed_ips.length > 0 && (
                <div className="flex items-center gap-1 text-gray-600">
                  <span className="font-medium">IPs:</span>
                  <span>{apiKey.allowed_ips.join(', ')}</span>
                </div>
              )}
              {apiKey.allowed_domains.length > 0 && (
                <div className="flex items-center gap-1 text-gray-600">
                  <Globe className="h-3 w-3" />
                  <span className="font-medium">Domains:</span>
                  <span>{apiKey.allowed_domains.join(', ')}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Usage Stats */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
            <Activity className="h-4 w-4" />
            Usage Statistics
          </label>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Total Requests:</span>
              <span className="ml-2 font-semibold">{apiKey.total_requests.toLocaleString()}</span>
            </div>
            {apiKey.last_used_at && (
              <div>
                <span className="text-gray-600">Last Used:</span>
                <span className="ml-2 font-semibold">
                  {formatDistanceToNow(new Date(apiKey.last_used_at), { addSuffix: true })}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Expiration */}
        {apiKey.expires_at && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              Expiration
            </label>
            <div className="text-sm">
              <span className={`font-semibold ${isExpired ? 'text-red-600' : 'text-gray-900'}`}>
                {new Date(apiKey.expires_at).toLocaleDateString()} at{' '}
                {new Date(apiKey.expires_at).toLocaleTimeString()}
              </span>
              {!isExpired && (
                <span className="ml-2 text-gray-600">
                  ({formatDistanceToNow(new Date(apiKey.expires_at), { addSuffix: true })})
                </span>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end pt-2 border-t">
          {apiKey.status !== 'revoked' && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => onRevoke(apiKey.id)}
              className="text-xs"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Revoke
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
