'use client';

import { useState, useEffect, use<PERSON>allback, Suspense } from 'react';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CpuChipIcon,
  CalendarIcon,
  FunnelIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  SparklesIcon,
  BoltIcon,
  LightBulbIcon,
  BellIcon,
  ChartPieIcon
} from '@heroicons/react/24/outline';

interface AnalyticsData {
  summary: {
    total_requests: number;
    successful_requests: number;
    success_rate: number;
    total_cost: number;
    total_input_tokens: number;
    total_output_tokens: number;
    average_cost_per_request: number;
  };
  grouped_data: Array<{
    name: string;
    requests: number;
    cost: number;
    input_tokens: number;
    output_tokens: number;
    success_rate?: number;
    period?: string; // For time series data
  }>;
}

interface CustomApiConfig {
  id: string;
  name: string;
}

interface TimeSeriesData {
  period: string;
  cost: number;
  requests: number;
  tokens: number;
}

interface LatencyData {
  provider: string;
  avg_latency: number;
  p95_latency: number;
  requests: number;
}

interface CostAlert {
  type: 'warning' | 'danger' | 'info';
  title: string;
  message: string;
  value?: string;
}

interface UsageRecommendation {
  type: 'cost_optimization' | 'performance' | 'efficiency';
  title: string;
  description: string;
  potential_savings?: number;
  icon: any;
}

function AnalyticsPageContent() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [modelAnalytics, setModelAnalytics] = useState<AnalyticsData | null>(null);
  const [configAnalytics, setConfigAnalytics] = useState<AnalyticsData | null>(null);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [previousPeriodData, setPreviousPeriodData] = useState<AnalyticsData | null>(null);
  const [customConfigs, setCustomConfigs] = useState<CustomApiConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [timeRange, setTimeRange] = useState('30');
  const [selectedConfig, setSelectedConfig] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Budget settings
  const [monthlyBudget, setMonthlyBudget] = useState<number>(100); // Default $100/month
  const [showBudgetSettings, setShowBudgetSettings] = useState(false);

  useEffect(() => {
    fetchCustomConfigs();
  }, []);

  const fetchCustomConfigs = async () => {
    try {
      const response = await fetch('/api/custom-configs');
      if (response.ok) {
        const configs = await response.json();
        setCustomConfigs(configs);
      }
    } catch (err) {
      console.error('Error fetching configs:', err);
    }
  };

  const fetchAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters for current period
      const params = new URLSearchParams();
      let currentStartDate: Date;
      let currentEndDate: Date = new Date();

      if (startDate && endDate) {
        currentStartDate = new Date(startDate);
        currentEndDate = new Date(endDate);
        params.append('startDate', currentStartDate.toISOString());
        params.append('endDate', currentEndDate.toISOString());
      } else if (timeRange) {
        currentStartDate = new Date();
        currentStartDate.setDate(currentStartDate.getDate() - parseInt(timeRange));
        params.append('startDate', currentStartDate.toISOString());
      }

      if (selectedConfig) {
        params.append('customApiConfigId', selectedConfig);
      }

      // Build query parameters for previous period (for comparison)
      const prevParams = new URLSearchParams();
      const periodLength = currentEndDate.getTime() - currentStartDate!.getTime();
      const prevStartDate = new Date(currentStartDate!.getTime() - periodLength);
      const prevEndDate = new Date(currentStartDate!.getTime());

      prevParams.append('startDate', prevStartDate.toISOString());
      prevParams.append('endDate', prevEndDate.toISOString());
      if (selectedConfig) {
        prevParams.append('customApiConfigId', selectedConfig);
      }

      // Fetch multiple analytics views
      const [
        providerResponse,
        modelResponse,
        timeSeriesResponse,
        previousPeriodResponse
      ] = await Promise.all([
        fetch(`/api/analytics/summary?${params.toString()}&groupBy=provider`),
        fetch(`/api/analytics/summary?${params.toString()}&groupBy=model`),
        fetch(`/api/analytics/summary?${params.toString()}&groupBy=day`),
        fetch(`/api/analytics/summary?${prevParams.toString()}&groupBy=day`)
      ]);

      if (!providerResponse.ok || !modelResponse.ok || !timeSeriesResponse.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const providerData = await providerResponse.json();
      const modelData = await modelResponse.json();
      const timeSeriesResult = await timeSeriesResponse.json();
      const previousPeriodResult = previousPeriodResponse.ok ? await previousPeriodResponse.json() : null;

      setAnalyticsData(providerData);
      setModelAnalytics(modelData);
      setPreviousPeriodData(previousPeriodResult);

      // Process time series data
      const processedTimeSeries: TimeSeriesData[] = timeSeriesResult.grouped_data.map((item: any) => ({
        period: item.period,
        cost: item.cost,
        requests: item.requests,
        tokens: item.input_tokens + item.output_tokens
      }));
      setTimeSeriesData(processedTimeSeries);

    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching analytics:', err);
    } finally {
      setLoading(false);
    }
  }, [timeRange, selectedConfig, startDate, endDate]);

  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData, timeRange, selectedConfig, startDate, endDate]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return { percentage: 0, isPositive: true };
    const percentage = ((current - previous) / previous) * 100;
    return { percentage: Math.abs(percentage), isPositive: percentage >= 0 };
  };

  const generateCostAlerts = (): CostAlert[] => {
    const alerts: CostAlert[] = [];
    const summary = analyticsData?.summary;

    if (!summary) return alerts;

    // Budget alert
    const projectedMonthlyCost = (summary.total_cost / parseInt(timeRange)) * 30;
    if (projectedMonthlyCost > monthlyBudget * 0.8) {
      alerts.push({
        type: projectedMonthlyCost > monthlyBudget ? 'danger' : 'warning',
        title: projectedMonthlyCost > monthlyBudget ? 'Budget Exceeded' : 'Budget Warning',
        message: `Projected monthly cost: ${formatCurrency(projectedMonthlyCost)} (Budget: ${formatCurrency(monthlyBudget)})`,
        value: `${((projectedMonthlyCost / monthlyBudget) * 100).toFixed(0)}%`
      });
    }

    // Success rate alert
    if (summary.success_rate < 95) {
      alerts.push({
        type: summary.success_rate < 90 ? 'danger' : 'warning',
        title: 'Low Success Rate',
        message: `Current success rate is ${summary.success_rate.toFixed(1)}%. Consider reviewing failed requests.`,
        value: `${summary.success_rate.toFixed(1)}%`
      });
    }

    // High cost per request alert
    if (summary.average_cost_per_request > 0.01) {
      alerts.push({
        type: 'warning',
        title: 'High Cost Per Request',
        message: `Average cost per request is ${formatCurrency(summary.average_cost_per_request)}. Consider optimizing model usage.`,
        value: formatCurrency(summary.average_cost_per_request)
      });
    }

    return alerts;
  };

  const generateRecommendations = (): UsageRecommendation[] => {
    const recommendations: UsageRecommendation[] = [];
    const summary = analyticsData?.summary;
    const providers = analyticsData?.grouped_data || [];
    const models = modelAnalytics?.grouped_data || [];

    if (!summary) return recommendations;

    // Cost optimization recommendations
    const mostExpensiveModel = models.sort((a, b) => b.cost - a.cost)[0];
    if (mostExpensiveModel && mostExpensiveModel.cost > summary.total_cost * 0.3) {
      const potentialSavings = mostExpensiveModel.cost * 0.2; // Assume 20% savings possible
      recommendations.push({
        type: 'cost_optimization',
        title: 'Optimize Expensive Model Usage',
        description: `${mostExpensiveModel.name} accounts for ${((mostExpensiveModel.cost / summary.total_cost) * 100).toFixed(1)}% of your costs. Consider using a more cost-effective alternative for simpler tasks.`,
        potential_savings: potentialSavings,
        icon: CurrencyDollarIcon
      });
    }

    // Performance recommendations
    if (summary.success_rate < 98) {
      recommendations.push({
        type: 'performance',
        title: 'Improve Request Reliability',
        description: `Your success rate is ${summary.success_rate.toFixed(1)}%. Implement retry logic and error handling to improve reliability.`,
        icon: CheckCircleIcon
      });
    }

    // Efficiency recommendations
    const avgTokensPerRequest = (summary.total_input_tokens + summary.total_output_tokens) / summary.total_requests;
    if (avgTokensPerRequest > 1000) {
      recommendations.push({
        type: 'efficiency',
        title: 'Optimize Token Usage',
        description: `Average ${formatNumber(avgTokensPerRequest)} tokens per request. Consider breaking down large prompts or using more efficient models.`,
        icon: CpuChipIcon
      });
    }

    return recommendations;
  };

  const resetFilters = () => {
    setTimeRange('30');
    setSelectedConfig('');
    setStartDate('');
    setEndDate('');
  };

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <h1 className="text-4xl font-bold mb-6">📊 Advanced Analytics</h1>
        <div className="card p-6 text-center">
          <p className="text-red-600 mb-4">Error loading analytics: {error}</p>
          <button onClick={fetchAnalyticsData} className="btn-primary">
            Retry
          </button>
        </div>
      </div>
    );
  }

  const summary = analyticsData?.summary;
  const costEfficiency = summary ? summary.total_cost / Math.max(summary.successful_requests, 1) : 0;
  const projectedMonthlyCost = summary ? (summary.total_cost / parseInt(timeRange)) * 30 : 0;

  // Generate insights
  const costAlerts = generateCostAlerts();
  const recommendations = generateRecommendations();

  // Calculate trends vs previous period
  const previousSummary = previousPeriodData?.summary;
  const costTrend = previousSummary ? calculateTrend(summary?.total_cost || 0, previousSummary.total_cost) : null;
  const requestTrend = previousSummary ? calculateTrend(summary?.total_requests || 0, previousSummary.total_requests) : null;

  // Professional Line Chart Component (like reference)
  const ProfessionalLineChart = ({ data }: { data: TimeSeriesData[] }) => {
    if (!data.length) {
      return (
        <div className="h-64 flex items-center justify-center">
          <div className="text-center">
            <ChartBarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500">No data available</p>
          </div>
        </div>
      );
    }

    const maxCost = Math.max(...data.map(d => d.cost));
    const minCost = Math.min(...data.map(d => d.cost));
    const maxRequests = Math.max(...data.map(d => d.requests));
    const minRequests = Math.min(...data.map(d => d.requests));

    const costRange = maxCost - minCost || 1;
    const requestRange = maxRequests - minRequests || 1;

    return (
      <div className="relative h-64 w-full">
        <svg className="w-full h-full" viewBox="0 0 600 200">
          <defs>
            {/* Gradients for smooth fills */}
            <linearGradient id="costGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.05" />
            </linearGradient>
            <linearGradient id="requestGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.05" />
            </linearGradient>

            {/* Subtle grid pattern */}
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f1f5f9" strokeWidth="0.5"/>
            </pattern>
          </defs>

          {/* Grid background */}
          <rect width="600" height="200" fill="url(#grid)" />

          {/* Cost area fill */}
          <polygon
            fill="url(#costGradient)"
            points={`0,200 ${data.map((d, i) => {
              const x = (i / Math.max(data.length - 1, 1)) * 600;
              const y = 200 - ((d.cost - minCost) / costRange) * 160;
              return `${x},${y}`;
            }).join(' ')} 600,200`}
          />

          {/* Request area fill */}
          <polygon
            fill="url(#requestGradient)"
            points={`0,200 ${data.map((d, i) => {
              const x = (i / Math.max(data.length - 1, 1)) * 600;
              const y = 200 - ((d.requests - minRequests) / requestRange) * 160;
              return `${x},${y}`;
            }).join(' ')} 600,200`}
          />

          {/* Cost line - Purple like reference */}
          <polyline
            fill="none"
            stroke="#8b5cf6"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            points={data.map((d, i) => {
              const x = (i / Math.max(data.length - 1, 1)) * 600;
              const y = 200 - ((d.cost - minCost) / costRange) * 160;
              return `${x},${y}`;
            }).join(' ')}
          />

          {/* Request line - Cyan like reference */}
          <polyline
            fill="none"
            stroke="#06b6d4"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            points={data.map((d, i) => {
              const x = (i / Math.max(data.length - 1, 1)) * 600;
              const y = 200 - ((d.requests - minRequests) / requestRange) * 160;
              return `${x},${y}`;
            }).join(' ')}
          />

          {/* Data points with hover effects */}
          {data.map((d, i) => {
            const x = (i / Math.max(data.length - 1, 1)) * 600;
            const costY = 200 - ((d.cost - minCost) / costRange) * 160;
            const requestY = 200 - ((d.requests - minRequests) / requestRange) * 160;
            return (
              <g key={i}>
                <circle
                  cx={x}
                  cy={costY}
                  r="3"
                  fill="#8b5cf6"
                  stroke="white"
                  strokeWidth="2"
                  className="hover:r-5 transition-all duration-200 cursor-pointer"
                >
                  <title>{`${d.period}: ${formatCurrency(d.cost)}`}</title>
                </circle>
                <circle
                  cx={x}
                  cy={requestY}
                  r="3"
                  fill="#06b6d4"
                  stroke="white"
                  strokeWidth="2"
                  className="hover:r-5 transition-all duration-200 cursor-pointer"
                >
                  <title>{`${d.period}: ${formatNumber(d.requests)} requests`}</title>
                </circle>
              </g>
            );
          })}
        </svg>
      </div>
    );
  };

  // Professional Donut Chart Component (like reference)
  const ProfessionalDonutChart = ({ data, total }: { data: any[], total: number }) => {
    if (!data.length) {
      return (
        <div className="h-48 flex items-center justify-center">
          <div className="text-center">
            <ChartPieIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500">No data available</p>
          </div>
        </div>
      );
    }

    // Professional color palette like the reference
    const colors = ['#f43f5e', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444'];
    const radius = 70;
    const strokeWidth = 16;
    const normalizedRadius = radius - strokeWidth * 0.5;
    const circumference = normalizedRadius * 2 * Math.PI;

    let cumulativePercentage = 0;

    return (
      <div className="flex flex-col items-center">
        <div className="relative mb-6">
          <svg width="160" height="160" className="transform -rotate-90">
            {/* Background circle with subtle shadow */}
            <circle
              cx="80"
              cy="80"
              r={normalizedRadius}
              stroke="#f8fafc"
              strokeWidth={strokeWidth}
              fill="transparent"
            />

            {/* Data segments */}
            {data.map((item, index) => {
              const percentage = (item.cost / total) * 100;
              const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`;
              const strokeDashoffset = -((cumulativePercentage / 100) * circumference);

              cumulativePercentage += percentage;

              return (
                <circle
                  key={index}
                  cx="80"
                  cy="80"
                  r={normalizedRadius}
                  stroke={colors[index % colors.length]}
                  strokeWidth={strokeWidth}
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  fill="transparent"
                  strokeLinecap="round"
                  className="transition-all duration-300 hover:opacity-80 cursor-pointer"
                  style={{
                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                  }}
                />
              );
            })}
          </svg>

          {/* Clean center - no ugly cost display */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{data.length}</div>
              <div className="text-sm text-gray-500">Providers</div>
            </div>
          </div>
        </div>

        {/* Clean Legend */}
        <div className="space-y-2 w-full">
          {data.slice(0, 4).map((item, index) => {
            const percentage = ((item.cost / total) * 100).toFixed(1);
            return (
              <div key={index} className="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors duration-150">
                <div className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-3 shadow-sm"
                    style={{ backgroundColor: colors[index] }}
                  ></div>
                  <span className="text-sm font-medium text-gray-700 capitalize">{item.name}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">{percentage}%</div>
                  <div className="text-xs text-gray-500">{formatCurrency(item.cost)}</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-[#faf8f5] p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Analytics Overview</h1>
            <p className="text-gray-600">
              Track your LLM usage, costs, and performance across all providers
            </p>
          </div>

          <div className="flex items-center space-x-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
            </select>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200"
            >
              <FunnelIcon className="h-4 w-4 mr-2 inline" />
              Filters
            </button>
          </div>
        </div>

      {/* Advanced Filters */}
      {showFilters && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Advanced Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Time Range</label>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="input-field"
              >
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">API Configuration</label>
              <select
                value={selectedConfig}
                onChange={(e) => setSelectedConfig(e.target.value)}
                className="input-field"
              >
                <option value="">All Configurations</option>
                {customConfigs.map((config) => (
                  <option key={config.id} value={config.id}>
                    {config.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="input-field"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="input-field"
              />
            </div>
          </div>

          <div className="mt-4 flex space-x-3">
            <button onClick={fetchAnalyticsData} className="btn-primary">
              Apply Filters
            </button>
            <button onClick={resetFilters} className="btn-secondary">
              Reset Filters
            </button>
          </div>
        </div>
      )}



        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Cost Trends Chart */}
          <div className="lg:col-span-2 bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Usage Analytics</h3>
                <p className="text-sm text-gray-500">Cost trends over time</p>
              </div>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                  <span className="text-gray-600">Cost</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-cyan-500 rounded-full mr-2"></div>
                  <span className="text-gray-600">Requests</span>
                </div>
              </div>
            </div>

            <ProfessionalLineChart data={timeSeriesData} />

            <div className="mt-6 pt-6 border-t border-gray-100">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {formatCurrency(summary?.total_cost || 0)}
                </div>
                <div className="text-sm text-gray-500">Total spend this period</div>
              </div>
            </div>
          </div>

          {/* Provider Distribution Donut Chart */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-1">Cost Distribution</h3>
              <p className="text-sm text-gray-500">By provider</p>
            </div>

            {analyticsData?.grouped_data.length && summary ? (
              <ProfessionalDonutChart
                data={analyticsData.grouped_data}
                total={summary.total_cost}
              />
            ) : (
              <div className="text-center py-8">
                <ChartPieIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500">No provider data available</p>
              </div>
            )}
          </div>
        </div>

        {/* Key Metrics Cards */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Cost */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 bg-orange-50 rounded-lg">
                  <CurrencyDollarIcon className="h-5 w-5 text-[#ff6b35]" />
                </div>
                <div className="text-right">
                  {costTrend && (
                    <div className={`flex items-center text-sm ${costTrend.isPositive ? 'text-red-500' : 'text-green-500'}`}>
                      {costTrend.isPositive ? (
                        <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />
                      ) : (
                        <ArrowTrendingDownIcon className="h-4 w-4 mr-1" />
                      )}
                      {costTrend.percentage.toFixed(1)}%
                    </div>
                  )}
                </div>
              </div>
              <div className="space-y-1">
                <h3 className="text-2xl font-bold text-gray-900">
                  {formatCurrency(summary.total_cost)}
                </h3>
                <p className="text-sm text-gray-600">Total spend</p>
                <p className="text-xs text-gray-500">
                  {formatCurrency(summary.average_cost_per_request)} avg per request
                </p>
              </div>
            </div>

            {/* Success Rate */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-2 rounded-lg ${summary.success_rate >= 95 ? 'bg-green-50' : 'bg-red-50'}`}>
                  <CheckCircleIcon className={`h-5 w-5 ${summary.success_rate >= 95 ? 'text-green-600' : 'text-red-600'}`} />
                </div>
                <div className="text-right">
                  <div className={`flex items-center text-sm ${summary.success_rate >= 95 ? 'text-green-500' : 'text-red-500'}`}>
                    <SparklesIcon className="h-4 w-4 mr-1" />
                    {summary.success_rate >= 95 ? 'Excellent' : 'Needs attention'}
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                <h3 className="text-2xl font-bold text-gray-900">
                  {summary.success_rate.toFixed(1)}%
                </h3>
                <p className="text-sm text-gray-600">Success rate</p>
                <p className="text-xs text-gray-500">
                  {formatNumber(summary.successful_requests)} of {formatNumber(summary.total_requests)} requests
                </p>
              </div>
            </div>

            {/* Token Usage */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 bg-purple-50 rounded-lg">
                  <CpuChipIcon className="h-5 w-5 text-purple-600" />
                </div>
                <div className="text-right">
                  <div className="flex items-center text-sm text-purple-500">
                    <SparklesIcon className="h-4 w-4 mr-1" />
                    Tokens
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                <h3 className="text-2xl font-bold text-gray-900">
                  {formatNumber(summary.total_input_tokens + summary.total_output_tokens)}
                </h3>
                <p className="text-sm text-gray-600">Total tokens</p>
                <p className="text-xs text-gray-500">
                  {formatNumber(summary.total_input_tokens)} input • {formatNumber(summary.total_output_tokens)} output
                </p>
              </div>
            </div>

            {/* Projected Monthly Cost */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 bg-indigo-50 rounded-lg">
                  <ArrowTrendingUpIcon className="h-5 w-5 text-indigo-600" />
                </div>
                <div className="text-right">
                  <div className={`flex items-center text-sm ${projectedMonthlyCost > monthlyBudget ? 'text-red-500' : 'text-green-500'}`}>
                    {projectedMonthlyCost > monthlyBudget ? (
                      <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                    ) : (
                      <CheckCircleIcon className="h-4 w-4 mr-1" />
                    )}
                    {projectedMonthlyCost > monthlyBudget ? 'Over budget' : 'On track'}
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                <h3 className="text-2xl font-bold text-gray-900">
                  {formatCurrency(projectedMonthlyCost)}
                </h3>
                <p className="text-sm text-gray-600">Projected monthly</p>
                <p className="text-xs text-gray-500">
                  Budget: {formatCurrency(monthlyBudget)}
                </p>
              </div>
            </div>
        </div>
      )}

        {/* Detailed Analytics Tables */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Provider Performance Table */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-1">Provider Performance</h3>
              <p className="text-sm text-gray-500">Detailed breakdown by provider</p>
            </div>
            {analyticsData?.grouped_data.length ? (
              <div className="overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-100">
                      <th className="text-left py-3 text-sm font-medium text-gray-600">Provider</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-600">Cost</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-600">Requests</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-600">Share</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-50">
                    {analyticsData.grouped_data
                      .sort((a, b) => b.cost - a.cost)
                      .map((provider, index) => {
                        const colors = ['#ff6b35', '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
                        const percentage = ((provider.cost / (summary?.total_cost || 1)) * 100).toFixed(1);
                        return (
                          <tr key={provider.name} className="hover:bg-gray-50 transition-colors duration-150">
                            <td className="py-4">
                              <div className="flex items-center">
                                <div
                                  className="w-3 h-3 rounded-full mr-3"
                                  style={{ backgroundColor: colors[index % colors.length] }}
                                ></div>
                                <span className="font-medium text-gray-900 capitalize">{provider.name}</span>
                              </div>
                            </td>
                            <td className="py-4 text-right font-semibold text-gray-900">
                              {formatCurrency(provider.cost)}
                            </td>
                            <td className="py-4 text-right text-gray-600">
                              {formatNumber(provider.requests)}
                            </td>
                            <td className="py-4 text-right">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {percentage}%
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <ChartPieIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No provider data available</p>
              </div>
            )}
          </div>

          {/* Model Performance Table */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-1">Top Models</h3>
              <p className="text-sm text-gray-500">Most expensive models by cost</p>
            </div>

            {modelAnalytics?.grouped_data.length ? (
              <div className="overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-100">
                      <th className="text-left py-3 text-sm font-medium text-gray-600">Model</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-600">Cost</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-600">Requests</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-600">Avg/Request</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-50">
                    {modelAnalytics.grouped_data
                      .sort((a, b) => b.cost - a.cost)
                      .slice(0, 5)
                      .map((model, index) => {
                        const colors = ['#ff6b35', '#3b82f6', '#10b981', '#f59e0b', '#ef4444'];
                        const avgCost = model.cost / Math.max(model.requests, 1);
                        return (
                          <tr key={model.name} className="hover:bg-gray-50 transition-colors duration-150">
                            <td className="py-4">
                              <div className="flex items-center">
                                <div
                                  className="w-3 h-3 rounded-full mr-3"
                                  style={{ backgroundColor: colors[index % colors.length] }}
                                ></div>
                                <div>
                                  <span className="font-medium text-gray-900">{model.name.length > 20 ? model.name.substring(0, 20) + '...' : model.name}</span>
                                  <div className="text-xs text-gray-500 mt-1">
                                    {formatNumber(model.input_tokens + model.output_tokens)} tokens
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="py-4 text-right font-semibold text-gray-900">
                              {formatCurrency(model.cost)}
                            </td>
                            <td className="py-4 text-right text-gray-600">
                              {formatNumber(model.requests)}
                            </td>
                            <td className="py-4 text-right text-gray-600">
                              {formatCurrency(avgCost)}
                            </td>
                          </tr>
                        );
                      })}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <CpuChipIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No model data available</p>
              </div>
            )}
          </div>
        </div>

      {/* Performance Insights */}
      <div className="card p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
          <BoltIcon className="h-5 w-5 mr-2 text-green-600" />
          Performance Insights
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 rounded-lg bg-green-50">
            <div className="text-2xl font-bold text-green-600 mb-2">
              {summary ? formatNumber(summary.total_input_tokens + summary.total_output_tokens) : 0}
            </div>
            <div className="text-sm text-gray-600">Total Tokens Processed</div>
            <div className="text-xs text-gray-500 mt-1">
              {summary ? `${formatNumber(summary.total_input_tokens)} in • ${formatNumber(summary.total_output_tokens)} out` : ''}
            </div>
          </div>

          <div className="text-center p-4 rounded-lg bg-blue-50">
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {summary ? formatNumber(summary.total_requests) : 0}
            </div>
            <div className="text-sm text-gray-600">Total API Requests</div>
            <div className="text-xs text-gray-500 mt-1">
              {summary ? `${(summary.total_requests / parseInt(timeRange)).toFixed(1)} per day avg` : ''}
            </div>
          </div>

          <div className="text-center p-4 rounded-lg bg-purple-50">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {summary ? formatCurrency(summary.average_cost_per_request) : '$0.00'}
            </div>
            <div className="text-sm text-gray-600">Average Cost per Request</div>
            <div className="text-xs text-gray-500 mt-1">
              Across all providers
            </div>
          </div>
        </div>
      </div>

        {/* Smart Insights & Recommendations */}
        {(costAlerts.length > 0 || recommendations.length > 0) && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Cost Alerts */}
            {costAlerts.length > 0 && (
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1 flex items-center">
                    <BellIcon className="h-5 w-5 mr-2 text-[#ff6b35]" />
                    Alerts & Warnings
                  </h3>
                  <p className="text-sm text-gray-500">Important notifications about your usage</p>
                </div>
                <div className="space-y-4">
                  {costAlerts.map((alert, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-xl border-l-4 ${
                        alert.type === 'danger' ? 'border-red-500 bg-red-50' :
                        alert.type === 'warning' ? 'border-yellow-500 bg-yellow-50' :
                        'border-blue-500 bg-blue-50'
                      }`}
                    >
                      <div className="flex items-start">
                        <ExclamationTriangleIcon className={`h-5 w-5 mr-3 mt-0.5 ${
                          alert.type === 'danger' ? 'text-red-600' :
                          alert.type === 'warning' ? 'text-yellow-600' :
                          'text-blue-600'
                        }`} />
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{alert.title}</h4>
                          <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                          {alert.value && (
                            <p className={`text-lg font-bold mt-2 ${
                              alert.type === 'danger' ? 'text-red-600' :
                              alert.type === 'warning' ? 'text-yellow-600' :
                              'text-blue-600'
                            }`}>
                              {alert.value}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recommendations */}
            {recommendations.length > 0 && (
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1 flex items-center">
                    <LightBulbIcon className="h-5 w-5 mr-2 text-yellow-500" />
                    Smart Recommendations
                  </h3>
                  <p className="text-sm text-gray-500">AI-powered optimization suggestions</p>
                </div>
                <div className="space-y-4">
                  {recommendations.map((rec, index) => (
                    <div key={index} className="p-4 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-100">
                      <div className="flex items-start">
                        <div className="p-2 rounded-lg bg-white shadow-sm mr-4">
                          <rec.icon className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{rec.title}</h4>
                          <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                          {rec.potential_savings && (
                            <div className="mt-2 inline-flex items-center px-3 py-1 rounded-full text-xs bg-green-100 text-green-800">
                              <SparklesIcon className="h-3 w-3 mr-1" />
                              Save {formatCurrency(rec.potential_savings)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Budget Management */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1 flex items-center">
                <CurrencyDollarIcon className="h-5 w-5 mr-2 text-green-600" />
                Budget Management
              </h3>
              <p className="text-sm text-gray-500">Monitor and control your spending</p>
            </div>
            <button
              onClick={() => setShowBudgetSettings(!showBudgetSettings)}
              className="px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200"
            >
              {showBudgetSettings ? 'Hide Settings' : 'Configure'}
            </button>
          </div>

          {showBudgetSettings && (
            <div className="mb-6 p-4 bg-gray-50 rounded-xl">
              <div className="max-w-xs">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Monthly Budget (USD)
                </label>
                <input
                  type="number"
                  value={monthlyBudget}
                  onChange={(e) => setMonthlyBudget(parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
                  placeholder="100.00"
                  step="0.01"
                  min="0"
                />
              </div>
              <div className="mt-3 text-xs text-gray-600">
                <p>• Alerts trigger at 80% (warning) and 100% (danger) of budget</p>
                <p>• Current projection: <strong>{formatCurrency(projectedMonthlyCost)}</strong></p>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 rounded-xl bg-blue-50">
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(monthlyBudget)}
              </div>
              <div className="text-sm text-gray-600">Monthly Budget</div>
            </div>

            <div className="text-center p-4 rounded-xl bg-orange-50">
              <div className="text-2xl font-bold text-[#ff6b35]">
                {formatCurrency(projectedMonthlyCost)}
              </div>
              <div className="text-sm text-gray-600">Projected Spend</div>
            </div>

            <div className="text-center p-4 rounded-xl bg-green-50">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(Math.max(0, monthlyBudget - projectedMonthlyCost))}
              </div>
              <div className="text-sm text-gray-600">
                {projectedMonthlyCost > monthlyBudget ? 'Over Budget' : 'Remaining'}
              </div>
            </div>
          </div>
        </div>

        {/* Historical Comparison */}
        {previousSummary && (
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-1 flex items-center">
                <CalendarIcon className="h-5 w-5 mr-2 text-purple-600" />
                Period Comparison
              </h3>
              <p className="text-sm text-gray-500">Performance vs previous period</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 rounded-xl bg-gray-50">
                <div className="text-sm font-medium text-gray-600 mb-2">Cost Change</div>
                <div className={`text-2xl font-bold ${costTrend?.isPositive ? 'text-red-600' : 'text-green-600'}`}>
                  {costTrend?.isPositive ? '+' : '-'}{costTrend?.percentage.toFixed(1)}%
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  {formatCurrency(previousSummary.total_cost)} → {formatCurrency(summary?.total_cost || 0)}
                </div>
              </div>

              <div className="text-center p-4 rounded-xl bg-gray-50">
                <div className="text-sm font-medium text-gray-600 mb-2">Request Volume</div>
                <div className={`text-2xl font-bold ${requestTrend?.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                  {requestTrend?.isPositive ? '+' : '-'}{requestTrend?.percentage.toFixed(1)}%
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  {formatNumber(previousSummary.total_requests)} → {formatNumber(summary?.total_requests || 0)}
                </div>
              </div>

              <div className="text-center p-4 rounded-xl bg-gray-50">
                <div className="text-sm font-medium text-gray-600 mb-2">Efficiency</div>
                <div className={`text-2xl font-bold ${
                  (summary?.average_cost_per_request || 0) < previousSummary.average_cost_per_request ? 'text-green-600' : 'text-red-600'
                }`}>
                  {((summary?.average_cost_per_request || 0) < previousSummary.average_cost_per_request) ? '↑' : '↓'}
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  {formatCurrency(previousSummary.average_cost_per_request)} → {formatCurrency(summary?.average_cost_per_request || 0)}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function AnalyticsPage() {
  return (
    <Suspense fallback={
      <div className="space-y-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>
      </div>
    }>
      <AnalyticsPageContent />
    </Suspense>
  );
}
