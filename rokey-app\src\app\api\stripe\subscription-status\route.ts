import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { STRIPE_ENV_INFO } from '@/lib/stripe-config';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');

    console.log('Subscription status API called for user:', userId);

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    // Check for network connectivity issues
    if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {
      console.log('Network connectivity issue detected, returning fallback response');
      return NextResponse.json({
        hasActiveSubscription: true,
        tier: 'free',
        status: 'active',
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        isFree: true,
        fallback: true,
        message: 'Using fallback due to network connectivity issues'
      });
    }

    // Get user's subscription and profile
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }

    // If no subscription found, user is on free tier
    if (subscriptionError || !subscription) {
      const tier = profile.subscription_tier || 'free';
      console.log('GET subscription-status - No active subscription found:', {
        userId,
        subscriptionError: subscriptionError?.message,
        profileTier: profile.subscription_tier,
        finalTier: tier
      });
      return NextResponse.json({
        hasActiveSubscription: tier !== 'free',
        tier,
        status: null,
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        isFree: tier === 'free',
      });
    }

    // Check if subscription is active
    const isActive = subscription.status === 'active';
    const currentPeriodEnd = new Date(subscription.current_period_end);
    const isExpired = currentPeriodEnd < new Date();

    console.log('GET subscription-status - Active subscription found:', {
      userId,
      tier: subscription.tier,
      status: subscription.status,
      isActive,
      isExpired,
      hasActiveSubscription: isActive && !isExpired
    });

    return NextResponse.json({
      hasActiveSubscription: isActive && !isExpired,
      tier: subscription.tier,
      status: subscription.status,
      currentPeriodEnd: subscription.current_period_end,
      currentPeriodStart: subscription.current_period_start,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      stripeCustomerId: subscription.stripe_customer_id,
      stripeSubscriptionId: subscription.stripe_subscription_id,
      isFree: subscription.is_free_tier || false,
    });

  } catch (error) {
    console.error('Error fetching subscription status:', error);

    // If there's a network error, return fallback response
    if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {
      console.log('Network error detected, returning fallback response');
      return NextResponse.json({
        hasActiveSubscription: true,
        tier: 'free',
        status: 'active',
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        isFree: true,
        fallback: true,
        message: 'Using fallback due to network error'
      });
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await req.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId' },
        { status: 400 }
      );
    }

    // Check for network connectivity issues
    if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {
      console.log('Network connectivity issue detected, returning fallback usage response');
      return NextResponse.json({
        tier: 'free',
        usage: {
          configurations: 0,
          apiKeys: 0,
          apiRequests: 0,
        },
        limits: getTierLimits('free'),
        canCreateConfig: true,
        canCreateApiKey: true,
        fallback: true,
        message: 'Using fallback due to network connectivity issues'
      });
    }

    // Get current usage for the user
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

    const { data: usage, error: usageError } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('month_year', currentMonth)
      .single();

    // Get user's current tier - check subscriptions table first, then user_profiles
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('tier, status')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    const { data: profile } = await supabase
      .from('user_profiles')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    // Use subscription tier if available and active, otherwise fall back to profile tier
    const tier = (subscription?.tier && subscription?.status === 'active')
      ? subscription.tier
      : (profile?.subscription_tier || 'free');

    console.log('POST subscription-status - User tier determination:', {
      userId,
      subscriptionTier: subscription?.tier,
      subscriptionStatus: subscription?.status,
      profileTier: profile?.subscription_tier,
      finalTier: tier
    });

    // Get configuration and API key counts
    const { count: configCount } = await supabase
      .from('custom_api_configs')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    const { count: apiKeyCount } = await supabase
      .from('api_keys')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    // Note: Workflow features will be added in future updates

    // Calculate tier limits
    const limits = getTierLimits(tier);

    return NextResponse.json({
      tier,
      usage: {
        configurations: configCount || 0,
        apiKeys: apiKeyCount || 0,
        apiRequests: usage?.api_requests_count || 0,
      },
      limits,
      canCreateConfig: (configCount || 0) < limits.configurations,
      canCreateApiKey: (apiKeyCount || 0) < limits.apiKeysPerConfig,
    });

  } catch (error) {
    console.error('Error fetching usage status:', error);

    // If there's a network error, return fallback response
    if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {
      console.log('Network error detected, returning fallback usage response');
      return NextResponse.json({
        tier: 'free',
        usage: {
          configurations: 0,
          apiKeys: 0,
          apiRequests: 0,
        },
        limits: getTierLimits('free'),
        canCreateConfig: true,
        canCreateApiKey: true,
        fallback: true,
        message: 'Using fallback due to network error'
      });
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getTierLimits(tier: string) {
  switch (tier) {
    case 'free':
      return {
        configurations: 1,
        apiKeysPerConfig: 3,
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: false,
        canUseCustomRoles: false,
        maxCustomRoles: 0,
        canUsePromptEngineering: false,
        canUseKnowledgeBase: false,
        knowledgeBaseDocuments: 0,
        canUseSemanticCaching: false,
      };
    case 'starter':
      return {
        configurations: 5,
        apiKeysPerConfig: 5,
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: true,
        canUseCustomRoles: true,
        maxCustomRoles: 3,
        canUsePromptEngineering: true,
        canUseKnowledgeBase: false,
        knowledgeBaseDocuments: 0,
        canUseSemanticCaching: false,
      };
    case 'professional':
      return {
        configurations: 20,
        apiKeysPerConfig: 15,
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: true,
        canUseCustomRoles: true,
        maxCustomRoles: 999999,
        canUsePromptEngineering: true,
        canUseKnowledgeBase: true,
        knowledgeBaseDocuments: 5,
        canUseSemanticCaching: true,
      };
    case 'enterprise':
      return {
        configurations: 999999, // Unlimited
        apiKeysPerConfig: 999999, // Unlimited
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: true,
        canUseCustomRoles: true,
        maxCustomRoles: 999999,
        canUsePromptEngineering: true,
        canUseKnowledgeBase: true,
        knowledgeBaseDocuments: 999999,
        canUseSemanticCaching: true,
      };
    default:
      return {
        configurations: 1,
        apiKeysPerConfig: 3,
        apiRequests: 999999,
        canUseAdvancedRouting: false,
        canUseCustomRoles: false,
        maxCustomRoles: 0,
        canUsePromptEngineering: false,
        canUseKnowledgeBase: false,
        knowledgeBaseDocuments: 0,
        canUseSemanticCaching: false,
      };
  }
}
