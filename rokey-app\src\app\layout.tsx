import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Suspense } from "react";
import "./globals.css";
import "../styles/design-system.css";
import ConditionalLayout from "@/components/ConditionalLayout";
import DocumentTitleUpdater from "@/components/DocumentTitleUpdater";
import PerformanceTracker from "@/components/PerformanceTracker";
import GlobalPrefetcher from "@/components/GlobalPrefetcher";
import Script from "next/script";
import { SpeedInsights } from '@vercel/speed-insights/next';

const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  variable: '--font-inter'
});

export const metadata: Metadata = {
  title: "RouKey - Smart LLM Key Router",
  description: "Advanced LLM API key routing and management",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        {/* Preload critical resources */}
        <link rel="preload" href="/api/custom-configs" as="fetch" crossOrigin="anonymous" />
        <link rel="preload" href="/api/system-status" as="fetch" crossOrigin="anonymous" />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />

        {/* Preconnect to critical origins */}
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />

        {/* Resource hints for navigation */}
        <link rel="prefetch" href="/dashboard" />
        <link rel="prefetch" href="/playground" />
        <link rel="prefetch" href="/logs" />
        <link rel="prefetch" href="/my-models" />
      </head>
      <body className="font-sans antialiased">
        <Suspense fallback={null}>
          <DocumentTitleUpdater />
        </Suspense>
        <GlobalPrefetcher />
        <PerformanceTracker
          enableUserBehaviorTracking={true}
          enableNavigationTracking={true}
          enableInteractionTracking={true}
        />
        <ConditionalLayout>
          {children}
        </ConditionalLayout>

        {/* Vercel Speed Insights */}
        <SpeedInsights />

        {/* Performance monitoring in development - temporarily disabled due to Supabase conflicts */}
        {process.env.NODE_ENV === 'development' && process.env.ENABLE_PERFORMANCE_MONITOR === 'true' && (
          <Script
            src="/performance-monitor.js"
            strategy="afterInteractive"
          />
        )}

        {/* Service Worker registration and cache preloading */}
        <Script id="sw-register" strategy="afterInteractive">
          {`
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('✅ Service Worker registered successfully');

                    // Preload critical data after SW is ready
                    if (window.location.pathname === '/') {
                      // Preload landing page data
                      fetch('/api/system-status').catch(() => {});

                      // Prefetch all critical pages immediately
                      setTimeout(() => {
                        const criticalPages = ['/features', '/pricing', '/about', '/auth/signin', '/auth/signup'];
                        criticalPages.forEach(page => {
                          const link = document.createElement('link');
                          link.rel = 'prefetch';
                          link.href = page;
                          document.head.appendChild(link);
                        });
                      }, 500); // Much faster prefetching
                    }
                  })
                  .catch(function(registrationError) {
                    console.warn('⚠️ Service Worker registration failed:', registrationError);
                  });
              });
            }
          `}
        </Script>
      </body>
    </html>
  );
}
