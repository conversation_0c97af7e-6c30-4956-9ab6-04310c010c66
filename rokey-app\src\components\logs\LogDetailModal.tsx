import { XMarkIcon } from '@heroicons/react/24/outline';

interface RequestLog {
  id: string;
  request_timestamp: string;
  custom_api_config_id: string | null;
  role_requested: string | null;
  role_used: string | null;
  llm_provider_name: string | null;
  llm_model_name: string | null;
  status_code: number | null;
  llm_provider_latency_ms: number | null;
  processing_duration_ms: number | null;
  input_tokens: number | null;
  output_tokens: number | null;
  cost: number | null;
  is_multimodal: boolean | null;
  ip_address: string | null;
  user_id: string | null;
  error_message: string | null;
  error_source: string | null;
  error_details_zod: string | null;
  llm_provider_status_code: number | null;
  request_payload_summary: any | null;
  response_payload_summary: any | null;
}

interface LogDetailModalProps {
  log: RequestLog;
  onClose: () => void;
  apiConfigNameMap: Record<string, string>;
}

const DetailItem: React.FC<{ label: string; value: React.ReactNode }> = ({ label, value }) => (
  <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
    <dt className="text-sm font-medium text-gray-700">{label}</dt>
    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words">{value !== null && value !== undefined && value !== '' ? value : 'N/A'}</dd>
  </div>
);

const PayloadDisplay: React.FC<{ title: string; data: any }> = ({ title, data }) => {
  let content;
  if (data === null || data === undefined) {
    content = 'N/A';
  } else if (typeof data === 'string') {
    content = data;
  } else {
    try {
      content = JSON.stringify(data, null, 2);
    } catch (e) {
      content = 'Invalid JSON data';
    }
  }
  return (
    <div className="py-2">
      <dt className="text-sm font-medium text-gray-700 mb-1">{title}</dt>
      <dd className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border">
        <pre className="whitespace-pre-wrap break-all">{content}</pre>
      </dd>
    </div>
  );
};

export default function LogDetailModal({ log, onClose, apiConfigNameMap }: LogDetailModalProps) {
  if (!log) return null;

  const getStatusDisplay = (statusCode: number | null) => {
    if (statusCode === null) return <span className="px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100">N/A</span>;
    if (statusCode >= 200 && statusCode < 300) return <span className="px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100">Success ({statusCode})</span>;
    if (statusCode >= 400) return <span className="px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100">Error ({statusCode})</span>;
    return <span className="px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100">Other ({statusCode})</span>;
  };

  const apiModelName = log.custom_api_config_id ? (apiConfigNameMap[log.custom_api_config_id] || 'Unknown Model') : 'N/A';

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out" onClick={onClose}>
      <div className="card max-w-2xl w-full max-h-[90vh] flex flex-col" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Log Details (ID: {log.id.substring(0,8)}...)</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200">
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-4 overflow-y-auto">
          <dl className="divide-y divide-gray-200">
            <DetailItem label="Timestamp" value={new Date(log.request_timestamp).toLocaleString()} />
            <DetailItem label="API Model Used" value={apiModelName} />
            <DetailItem label="Role Requested" value={log.role_requested} />
            <DetailItem label="Role Used" value={log.role_used} />
            <DetailItem label="Status" value={getStatusDisplay(log.status_code)} />
            <DetailItem label="LLM Provider" value={log.llm_provider_name} />
            <DetailItem
              label="LLM Model Name"
              value={(() => {
                // Check if this is an orchestration log
                if (log.role_used?.includes('RouKey_Multi Roles_') && log.response_payload_summary?.models_used) {
                  const models = log.response_payload_summary.models_used;
                  if (models.length <= 3) {
                    return models.join(', ');
                  } else {
                    return `${models.slice(0, 3).join(', ')}...`;
                  }
                }
                return log.llm_model_name;
              })()}
            />
            <DetailItem label="LLM Latency" value={log.llm_provider_latency_ms !== null ? `${log.llm_provider_latency_ms} ms` : 'N/A'} />
            <DetailItem label="RoKey Latency" value={log.processing_duration_ms !== null ? `${log.processing_duration_ms} ms` : 'N/A'} />
            <DetailItem label="Input Tokens" value={log.input_tokens !== null ? log.input_tokens : 'N/A'} />
            <DetailItem label="Output Tokens" value={log.output_tokens !== null ? log.output_tokens : 'N/A'} />
            <DetailItem label="Cost" value={log.cost !== null ? `$${log.cost.toFixed(6)}` : 'N/A'} />
            <DetailItem label="Multimodal Request" value={log.is_multimodal ? 'Yes' : 'No'} />
            <DetailItem label="IP Address" value={log.ip_address} />
            {log.user_id && <DetailItem label="User ID" value={log.user_id} />} 
            {log.error_message && (
              <>
                <DetailItem label="Error Message" value={log.error_message} />
                <DetailItem label="Error Source" value={log.error_source} />
              </>
            )}
            {log.llm_provider_status_code && <DetailItem label="LLM Provider Status" value={log.llm_provider_status_code} />}
          </dl>

          {log.request_payload_summary && 
            <PayloadDisplay title="Request Payload Summary" data={log.request_payload_summary} />
          }
          {log.response_payload_summary && 
            <PayloadDisplay title="Response Payload Summary" data={log.response_payload_summary} />
          }
          {log.error_details_zod && 
            <PayloadDisplay title="Zod Validation Error Details" data={log.error_details_zod} /> 
          }
        </div>

        <div className="p-6 border-t border-gray-200 text-right">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
} 