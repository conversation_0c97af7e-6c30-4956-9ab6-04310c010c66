'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ExclamationTriangleIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { SubscriptionTier } from '@/lib/stripe-client';
import { useRouter } from 'next/navigation';

interface LimitIndicatorProps {
  current: number;
  limit: number;
  label: string;
  tier: SubscriptionTier;
  showUpgradeHint?: boolean;
  className?: string;
}

export function LimitIndicator({
  current,
  limit,
  label,
  tier,
  showUpgradeHint = true,
  className = ''
}: LimitIndicatorProps) {
  const router = useRouter();
  const isUnlimited = limit >= 999999;
  const percentage = isUnlimited ? 0 : (current / limit) * 100;
  const isNearLimit = percentage >= 80;
  const isAtLimit = current >= limit && !isUnlimited;

  const getStatusColor = () => {
    if (isUnlimited) return 'text-green-600';
    if (isAtLimit) return 'text-red-600';
    if (isNearLimit) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getStatusIcon = () => {
    if (isUnlimited) return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
    if (isAtLimit) return <XCircleIcon className="w-5 h-5 text-red-600" />;
    if (isNearLimit) return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />;
    return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
  };

  const getProgressBarColor = () => {
    if (isUnlimited) return 'bg-green-500';
    if (isAtLimit) return 'bg-red-500';
    if (isNearLimit) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className={`flex items-center justify-between text-sm ${className}`}>
      <div className="flex items-center space-x-2">
        <span className="text-gray-600">{label}:</span>
        {!isUnlimited && (
          <div className="w-16 bg-gray-200 rounded-full h-1.5">
            <motion.div
              className={`h-1.5 rounded-full ${getProgressBarColor()}`}
              initial={{ width: 0 }}
              animate={{ width: `${Math.min(percentage, 100)}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
        )}
      </div>
      <div className="flex items-center space-x-1">
        <span className={`text-xs font-medium ${getStatusColor()}`}>
          {isUnlimited ? 'Unlimited' : `${current}/${limit}`}
        </span>
        {(isAtLimit || isNearLimit) && showUpgradeHint && (
          <button
            className="text-xs text-orange-600 hover:text-orange-700 underline ml-2"
            onClick={() => {
              router.push('/billing');
            }}
          >
            Upgrade
          </button>
        )}
      </div>
    </div>
  );
}
