'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentTextIcon,
  CodeBracketIcon,
  CogIcon,
  BoltIcon,
  KeyIcon,
  ChevronRightIcon,
  ClipboardDocumentIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SparklesIcon,
  ArrowTopRightOnSquareIcon,
  CircleStackIcon,
  ListBulletIcon,
  BookOpenIcon,
  PlayIcon,
  LightBulbIcon,
  CubeIcon,
  RocketLaunchIcon,
  QuestionMarkCircleIcon,
  ShieldCheckIcon,
  CpuChipIcon,
  UserGroupIcon,
  CloudIcon,
  ChartBarIcon,
  PhotoIcon,
  ArrowRightIcon,
  DocumentIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  ClockIcon,
  BellIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';

interface CodeBlockProps {
  children: string;
  language?: string;
  title?: string;
}

function CodeBlock({ children, language = 'javascript', title }: CodeBlockProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(children);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Enhanced syntax highlighting for different languages
  const highlightSyntax = (code: string, lang: string) => {
    const lines = code.split('\n');
    return lines.map((line, index) => {
      let highlightedLine = line;
      
      if (lang === 'javascript' || lang === 'typescript') {
        // Keywords
        highlightedLine = highlightedLine.replace(
          /\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new)\b/g,
          '<span class="text-purple-400">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
        // Comments
        highlightedLine = highlightedLine.replace(
          /(\/\/.*$)/g,
          '<span class="text-gray-500">$1</span>'
        );
        // Numbers
        highlightedLine = highlightedLine.replace(
          /\b(\d+\.?\d*)\b/g,
          '<span class="text-yellow-400">$1</span>'
        );
      } else if (lang === 'python') {
        // Python keywords
        highlightedLine = highlightedLine.replace(
          /\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False)\b/g,
          '<span class="text-purple-400">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
        // Comments
        highlightedLine = highlightedLine.replace(
          /(#.*$)/g,
          '<span class="text-gray-500">$1</span>'
        );
      } else if (lang === 'bash' || lang === 'shell') {
        // Bash commands
        highlightedLine = highlightedLine.replace(
          /\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed)\b/g,
          '<span class="text-blue-400">$1</span>'
        );
        // Flags
        highlightedLine = highlightedLine.replace(
          /(-[a-zA-Z]+)/g,
          '<span class="text-yellow-400">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
      } else if (lang === 'json') {
        // JSON keys
        highlightedLine = highlightedLine.replace(
          /"([^"]+)":/g,
          '<span class="text-blue-400">"$1"</span>:'
        );
        // JSON strings
        highlightedLine = highlightedLine.replace(
          /:\s*"([^"]*)"/g,
          ': <span class="text-green-400">"$1"</span>'
        );
        // JSON numbers
        highlightedLine = highlightedLine.replace(
          /:\s*(\d+\.?\d*)/g,
          ': <span class="text-yellow-400">$1</span>'
        );
        // JSON booleans
        highlightedLine = highlightedLine.replace(
          /:\s*(true|false|null)/g,
          ': <span class="text-purple-400">$1</span>'
        );
      }

      return (
        <div key={index} className="table-row">
          <span className="table-cell text-gray-500 text-right pr-4 select-none w-8">
            {index + 1}
          </span>
          <span 
            className="table-cell text-gray-100"
            dangerouslySetInnerHTML={{ __html: highlightedLine || ' ' }}
          />
        </div>
      );
    });
  };

  return (
    <div className="relative bg-gray-900 rounded-xl overflow-hidden border border-gray-300 shadow-lg">
      {title && (
        <div className="bg-gray-100 px-4 py-3 text-sm text-gray-700 border-b border-gray-300 flex items-center justify-between">
          <span className="font-medium">{title}</span>
          <span className="text-xs text-gray-500 uppercase tracking-wide">{language}</span>
        </div>
      )}
      <div className="relative">
        <pre className="p-4 overflow-x-auto text-sm font-mono leading-relaxed">
          <code className="table w-full">
            {highlightSyntax(children, language || 'javascript')}
          </code>
        </pre>
        <button
          onClick={handleCopy}
          className="absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-lg transition-all duration-200 border border-gray-300 shadow-sm"
          title="Copy to clipboard"
        >
          {copied ? (
            <CheckIcon className="h-4 w-4 text-green-600" />
          ) : (
            <ClipboardDocumentIcon className="h-4 w-4 text-gray-600" />
          )}
        </button>
      </div>
    </div>
  );
}

interface AlertProps {
  type: 'info' | 'warning' | 'tip';
  children: React.ReactNode;
}

function Alert({ type, children }: AlertProps) {
  const styles = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    tip: 'bg-green-50 border-green-200 text-green-800'
  };

  const iconStyles = {
    info: 'text-blue-600',
    warning: 'text-yellow-600',
    tip: 'text-green-600'
  };

  const icons = {
    info: InformationCircleIcon,
    warning: ExclamationTriangleIcon,
    tip: SparklesIcon
  };

  const Icon = icons[type];

  return (
    <div className={`border rounded-xl p-4 ${styles[type]}`}>
      <div className="flex items-start gap-3">
        <Icon className={`h-5 w-5 flex-shrink-0 mt-0.5 ${iconStyles[type]}`} />
        <div className="text-sm">{children}</div>
      </div>
    </div>
  );
}

export default function DocsPage() {
  const [activeSection, setActiveSection] = useState('overview');
  const [expandedSections, setExpandedSections] = useState<string[]>(['overview']);

  const sections = [
    {
      id: 'overview',
      title: 'Overview',
      icon: DocumentTextIcon,
      subsections: [
        { id: 'what-is-roukey', title: 'What is RouKey' },
        { id: 'key-benefits', title: 'Key Benefits' },
        { id: 'how-it-works', title: 'How It Works' },
        { id: 'architecture', title: 'Architecture' }
      ]
    },
    {
      id: 'getting-started',
      title: 'Getting Started',
      icon: PlayIcon,
      subsections: [
        { id: 'quickstart', title: 'Quickstart' },
        { id: 'installation', title: 'Installation' },
        { id: 'first-request', title: 'First Request' },
        { id: 'basic-setup', title: 'Basic Setup' }
      ]
    },
    {
      id: 'features',
      title: 'Features',
      icon: SparklesIcon,
      subsections: [
        { id: 'intelligent-routing', title: 'Intelligent Routing' },
        { id: 'multi-role-orchestration', title: 'Multi-Role Orchestration' },
        { id: 'semantic-caching', title: 'Semantic Caching' },
        { id: 'knowledge-base', title: 'Knowledge Base' },
        { id: 'custom-training', title: 'Custom Training' },
        { id: 'async-processing', title: 'Async Processing' },
        { id: 'analytics-monitoring', title: 'Analytics & Monitoring' }
      ]
    },
    {
      id: 'authentication',
      title: 'Authentication',
      icon: KeyIcon,
      subsections: [
        { id: 'api-keys', title: 'API Keys' },
        { id: 'auth-methods', title: 'Authentication Methods' },
        { id: 'security', title: 'Security' },
        { id: 'rate-limiting', title: 'Rate Limiting' }
      ]
    },
    {
      id: 'api-reference',
      title: 'API Reference',
      icon: CodeBracketIcon,
      subsections: [
        { id: 'base-url', title: 'Base URL' },
        { id: 'chat-completions', title: 'Chat Completions' },
        { id: 'streaming', title: 'Streaming' },
        { id: 'async-endpoints', title: 'Async Endpoints' },
        { id: 'image-support', title: 'Image Support' },
        { id: 'parameters', title: 'Parameters' },
        { id: 'responses', title: 'Responses' },
        { id: 'errors', title: 'Error Handling' }
      ]
    },
    {
      id: 'routing-strategies',
      title: 'Routing Strategies',
      icon: BoltIcon,
      subsections: [
        { id: 'strategy-overview', title: 'Strategy Overview' },
        { id: 'intelligent-role-routing', title: 'Intelligent Role Routing' },
        { id: 'complexity-routing', title: 'Complexity-Based Routing' },
        { id: 'cost-optimized', title: 'Cost-Optimized Routing' },
        { id: 'strict-fallback', title: 'Strict Fallback' },
        { id: 'ab-testing', title: 'A/B Testing' }
      ]
    },
    {
      id: 'configuration',
      title: 'Configuration',
      icon: CogIcon,
      subsections: [
        { id: 'dashboard-setup', title: 'Dashboard Setup' },
        { id: 'provider-keys', title: 'Provider API Keys' },
        { id: 'routing-config', title: 'Routing Configuration' },
        { id: 'custom-roles', title: 'Custom Roles' },
        { id: 'temperature-settings', title: 'Temperature Settings' }
      ]
    },
    {
      id: 'use-cases',
      title: 'Use Cases',
      icon: LightBulbIcon,
      subsections: [
        { id: 'development-coding', title: 'Development & Coding' },
        { id: 'content-creation', title: 'Content Creation' },
        { id: 'enterprise-apps', title: 'Enterprise Applications' },
        { id: 'educational-platforms', title: 'Educational Platforms' },
        { id: 'research-analysis', title: 'Research & Analysis' }
      ]
    },
    {
      id: 'examples',
      title: 'Examples',
      icon: CubeIcon,
      subsections: [
        { id: 'javascript-examples', title: 'JavaScript/Node.js' },
        { id: 'python-examples', title: 'Python' },
        { id: 'curl-examples', title: 'cURL' },
        { id: 'openai-sdk', title: 'OpenAI SDK Integration' }
      ]
    },
    {
      id: 'future-releases',
      title: 'Future Releases',
      icon: RocketLaunchIcon,
      subsections: [
        { id: 'q1-2025', title: 'Q1 2025 - Workflow Automation' },
        { id: 'q2-2025', title: 'Q2 2025 - Performance & Scale' },
        { id: 'q3-2025', title: 'Q3 2025 - AI-Powered Features' },
        { id: 'q4-2025', title: 'Q4 2025 - Enterprise Features' }
      ]
    },
    {
      id: 'faq',
      title: 'FAQ',
      icon: QuestionMarkCircleIcon,
      subsections: []
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      {/* Add top padding to account for fixed navbar */}
      <div className="pt-16 flex flex-col lg:flex-row min-h-screen">
        {/* Sidebar */}
        <div className="w-full lg:w-80 flex-shrink-0 bg-white border-r border-gray-200 shadow-sm lg:fixed lg:top-16 lg:left-0 lg:h-[calc(100vh-4rem)] lg:z-10 lg:overflow-y-auto">
          <div className="p-4 lg:p-6">
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">RouKey Documentation</h1>
              <p className="text-gray-600 text-sm">
                Complete guide to integrating and using RouKey's intelligent AI gateway
              </p>
            </div>

            <nav className="space-y-1">
              {sections.map((section) => {
                const Icon = section.icon;
                const isExpanded = expandedSections.includes(section.id);
                const hasSubsections = section.subsections && section.subsections.length > 0;

                return (
                  <div key={section.id}>
                    <button
                      onClick={() => {
                        setActiveSection(section.id);
                        if (hasSubsections) {
                          setExpandedSections(prev =>
                            isExpanded
                              ? prev.filter(id => id !== section.id)
                              : [...prev, section.id]
                          );
                        }
                      }}
                      className={`w-full flex items-center gap-3 px-4 py-3 text-sm rounded-xl transition-all duration-200 ${
                        activeSection === section.id
                          ? 'bg-[#ff6b35] text-white shadow-lg shadow-orange-500/25'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span className="flex-1 text-left">{section.title}</span>
                      {hasSubsections && (
                        <ChevronRightIcon
                          className={`h-4 w-4 transition-transform duration-200 ${
                            isExpanded ? 'rotate-90' : ''
                          }`}
                        />
                      )}
                    </button>

                    {hasSubsections && isExpanded && (
                      <div className="ml-8 mt-1 space-y-1">
                        {section.subsections.map((subsection) => (
                          <button
                            key={subsection.id}
                            onClick={() => setActiveSection(`${section.id}-${subsection.id}`)}
                            className={`w-full text-left px-3 py-2 text-xs rounded-lg transition-all duration-200 ${
                              activeSection === `${section.id}-${subsection.id}`
                                ? 'bg-[#ff6b35]/10 text-[#ff6b35] font-medium'
                                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                            }`}
                          >
                            {subsection.title}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
            </nav>

            <div className="mt-8 p-4 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
              <div className="flex items-center gap-2 mb-2">
                <SparklesIcon className="h-4 w-4 text-[#ff6b35]" />
                <span className="text-sm font-medium text-gray-900">Quick Start</span>
              </div>
              <p className="text-xs text-gray-600 mb-3">
                Get up and running in under 2 minutes
              </p>
              <button
                onClick={() => setActiveSection('overview')}
                className="w-full bg-[#ff6b35] hover:bg-[#e55a2b] text-white text-xs py-2 px-3 rounded-lg transition-colors"
              >
                Start Building
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white text-gray-900 min-h-screen lg:ml-80">
          <div className="max-w-6xl mx-auto p-3 lg:p-6 lg:py-8">
            <motion.div
              key={activeSection}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {activeSection === 'overview' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">
                      RouKey Overview
                    </h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      RouKey is a commercial BYOK (Bring Your Own Keys) framework that combines multiple AI routers and a gateway to optimize your LLM API usage through intelligent routing strategies.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-4 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <DocumentTextIcon className="h-5 w-5 text-[#ff6b35]" />
                        What is RouKey?
                      </h3>
                      <p className="text-gray-600 mb-4">Learn about RouKey's core concepts and capabilities</p>
                      <button
                        onClick={() => setActiveSection('overview-what-is-roukey')}
                        className="text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors"
                      >
                        Learn more <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 p-4 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <SparklesIcon className="h-5 w-5 text-blue-600" />
                        Key Benefits
                      </h3>
                      <p className="text-gray-600 mb-4">Discover how RouKey optimizes your AI operations</p>
                      <button
                        onClick={() => setActiveSection('overview-key-benefits')}
                        className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Explore benefits <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-green-100/50 p-4 rounded-xl border border-green-200 hover:shadow-lg hover:border-green-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CogIcon className="h-5 w-5 text-green-600" />
                        How It Works
                      </h3>
                      <p className="text-gray-600 mb-4">Understand RouKey's intelligent routing process</p>
                      <button
                        onClick={() => setActiveSection('overview-how-it-works')}
                        className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        See how <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 p-4 rounded-xl border border-purple-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CircleStackIcon className="h-5 w-5 text-purple-600" />
                        Architecture
                      </h3>
                      <p className="text-gray-600 mb-4">Deep dive into RouKey's technical architecture</p>
                      <button
                        onClick={() => setActiveSection('overview-architecture')}
                        className="text-purple-600 hover:text-purple-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        View architecture <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">How RouKey Works</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-lg">1</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Request Analysis</h3>
                        <p className="text-gray-600 text-sm">RouKey analyzes incoming requests using AI classification to understand complexity, role requirements, and optimal routing strategy.</p>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <div className="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-lg">2</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Intelligent Routing</h3>
                        <p className="text-gray-600 text-sm">Based on your configured strategy, RouKey routes the request to the optimal model considering cost, performance, and availability.</p>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-lg">3</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Response Delivery</h3>
                        <p className="text-gray-600 text-sm">RouKey delivers the response with automatic failover, retry logic, and comprehensive analytics for monitoring and optimization.</p>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Key Benefits</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                          <SparklesIcon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg mb-2">Cost Optimization</h3>
                          <p className="text-gray-600">Reduce API costs by up to 60% through intelligent routing to cost-effective models for appropriate tasks.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                          <BoltIcon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg mb-2">Enhanced Reliability</h3>
                          <p className="text-gray-600">Automatic failover and retry mechanisms ensure 99.9% uptime with seamless provider switching.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                          <CogIcon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg mb-2">Easy Integration</h3>
                          <p className="text-gray-600">Drop-in replacement for OpenAI API with full compatibility and additional routing capabilities.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                          <KeyIcon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg mb-2">Enterprise Security</h3>
                          <p className="text-gray-600">Your API keys stay secure with enterprise-grade encryption and never leave your control.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-6 rounded-xl text-white">
                    <h3 className="text-lg font-semibold mb-3">Ready to optimize your AI costs?</h3>
                    <p className="text-white/90 mb-4">
                      Join thousands of developers who have reduced their AI API costs by up to 60% with RouKey's intelligent routing.
                    </p>
                    <button
                      onClick={() => setActiveSection('getting-started')}
                      className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                    >
                      Get Started Now
                    </button>
                  </div>
                </div>
              )}

              {/* Overview Subsections */}
              {activeSection === 'overview-what-is-roukey' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">What is RouKey?</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      RouKey is an intelligent AI gateway that revolutionizes how you interact with multiple LLM providers.
                    </p>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Core Concept</h2>
                    <div className="prose prose-gray max-w-none space-y-6">
                      <p className="text-gray-600 leading-relaxed text-lg">
                        RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers (OpenAI, Anthropic, Google, DeepSeek, xAI, and more).
                        It automatically routes requests to the most appropriate model based on your configured strategies, providing cost optimization, improved reliability, and enhanced performance.
                      </p>
                      <p className="text-gray-600 leading-relaxed text-lg">
                        Unlike traditional API proxies, RouKey uses advanced AI classification to understand your requests and make intelligent routing decisions.
                        This means you get the right model for the right task, every time.
                      </p>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">BYOK Framework</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey follows a Bring Your Own Keys (BYOK) model, ensuring you maintain complete control over your API keys and costs.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <KeyIcon className="h-5 w-5 text-green-600" />
                            Your Keys, Your Control
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• You provide your own API keys</li>
                            <li>• Direct billing from providers</li>
                            <li>• No markup on API costs</li>
                            <li>• Full transparency</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                            Enterprise Security
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• AES-256 encryption</li>
                            <li>• Keys never leave your control</li>
                            <li>• No response logging</li>
                            <li>• SOC 2 compliance ready</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Supported Providers</h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mb-4">
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">OpenAI</div>
                        <div className="text-xs text-gray-600">GPT-4, GPT o3, o1</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Anthropic</div>
                        <div className="text-xs text-gray-600">Claude 4 Opus, Claude 3</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Google</div>
                        <div className="text-xs text-gray-600">Gemini 2.5 Pro, Flash</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">DeepSeek</div>
                        <div className="text-xs text-gray-600">v3, R1 0528</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">xAI</div>
                        <div className="text-xs text-gray-600">Grok Models</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">OpenRouter</div>
                        <div className="text-xs text-gray-600">300+ Models</div>
                      </div>
                    </div>
                    <p className="text-gray-600 text-center">
                      Access to <strong>300+ AI models</strong> from leading providers with unified API interface
                    </p>
                  </div>
                </div>
              )}

              {activeSection === 'overview-key-benefits' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">Key Benefits</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Discover how RouKey transforms your AI operations with intelligent routing and cost optimization.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-4">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-[#ff6b35] rounded-xl flex items-center justify-center">
                          <SparklesIcon className="h-5 w-5 text-white" />
                        </div>
                        <h2 className="text-xl font-bold text-gray-900">Cost Optimization</h2>
                      </div>
                      <div className="space-y-4">
                        <p className="text-gray-600 leading-relaxed">
                          Reduce API costs by up to 60% through intelligent routing to cost-effective models for appropriate tasks.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Complexity-based routing to cheaper models for simple tasks</li>
                          <li>• Semantic caching prevents duplicate API calls</li>
                          <li>• Real-time cost tracking and optimization</li>
                          <li>• No markup on provider costs</li>
                        </ul>
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <div className="text-green-800 font-medium mb-1">Average Savings</div>
                          <div className="text-2xl font-bold text-green-600">40-60%</div>
                          <div className="text-green-600 text-sm">on monthly API costs</div>
                        </div>
                      </div>
                    </div>

                    <div className="card p-4">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
                          <BoltIcon className="h-5 w-5 text-white" />
                        </div>
                        <h2 className="text-xl font-bold text-gray-900">Enhanced Reliability</h2>
                      </div>
                      <div className="space-y-4">
                        <p className="text-gray-600 leading-relaxed">
                          Automatic failover and retry mechanisms ensure 99.9% uptime with seamless provider switching.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Automatic failover between providers</li>
                          <li>• Intelligent retry logic with exponential backoff</li>
                          <li>• Real-time health monitoring</li>
                          <li>• Circuit breaker patterns</li>
                        </ul>
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <div className="text-blue-800 font-medium mb-1">Uptime SLA</div>
                          <div className="text-2xl font-bold text-blue-600">99.9%</div>
                          <div className="text-blue-600 text-sm">guaranteed availability</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'overview-how-it-works' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">How RouKey Works</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Understanding RouKey's intelligent routing process and decision-making algorithms.
                    </p>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Request Flow</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-xl">1</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-3">Request Analysis</h3>
                        <p className="text-gray-600 text-sm mb-4">RouKey analyzes incoming requests using AI classification to understand complexity, role requirements, and optimal routing strategy.</p>
                        <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">~50ms processing</div>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <div className="w-16 h-16 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-xl">2</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-3">Intelligent Routing</h3>
                        <p className="text-gray-600 text-sm mb-4">Based on your configured strategy, RouKey routes the request to the optimal model considering cost, performance, and availability.</p>
                        <div className="text-xs text-[#ff6b35] bg-[#ff6b35]/10 px-2 py-1 rounded">Real-time decisions</div>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <div className="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-xl">3</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-3">Response Delivery</h3>
                        <p className="text-gray-600 text-sm mb-4">RouKey delivers the response with automatic failover, retry logic, and comprehensive analytics for monitoring and optimization.</p>
                        <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">Sub-second latency</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">AI Classification Engine</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey uses advanced Gemini-powered classification to understand your requests and make optimal routing decisions.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CpuChipIcon className="h-5 w-5 text-purple-600" />
                            Complexity Analysis
                          </h3>
                          <p className="text-gray-600 mb-4">Analyzes prompt complexity on a 1-5 scale to determine the most cost-effective model.</p>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Simple (1-2)</span>
                              <span className="text-green-600">GPT-4, Gemini Flash</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Complex (4-5)</span>
                              <span className="text-blue-600">GPT o3, Claude 4 Opus</span>
                            </div>
                          </div>
                        </div>
                        <div className="p-6 bg-gradient-to-br from-indigo-50 to-indigo-100/50 rounded-xl border border-indigo-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <UserGroupIcon className="h-5 w-5 text-indigo-600" />
                            Role Detection
                          </h3>
                          <p className="text-gray-600 mb-4">Identifies the type of task to route to specialized models.</p>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Coding</span>
                              <span className="text-green-600">Deepseek R1 0528</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Writing</span>
                              <span className="text-blue-600">Claude 4 Opus</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'overview-architecture' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">RouKey Architecture</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Deep dive into RouKey's technical architecture and infrastructure design.
                    </p>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">System Architecture</h2>
                    <div className="space-y-6">
                      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                        <div className="text-center mb-6">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">RouKey Gateway Architecture</h3>
                          <p className="text-gray-600">High-level overview of RouKey's distributed system</p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-center">
                          <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
                            <div className="text-sm font-medium text-gray-900 mb-1">Client App</div>
                            <div className="text-xs text-gray-600">Your Application</div>
                          </div>
                          <div className="text-center">
                            <ChevronRightIcon className="h-6 w-6 text-gray-400 mx-auto" />
                          </div>
                          <div className="text-center p-4 bg-[#ff6b35]/10 rounded-lg border border-[#ff6b35]/20">
                            <div className="text-sm font-medium text-gray-900 mb-1">RouKey Gateway</div>
                            <div className="text-xs text-gray-600">Intelligent Router</div>
                          </div>
                          <div className="text-center">
                            <ChevronRightIcon className="h-6 w-6 text-gray-400 mx-auto" />
                          </div>
                          <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
                            <div className="text-sm font-medium text-gray-900 mb-1">LLM Providers</div>
                            <div className="text-xs text-gray-600">OpenAI, Anthropic, etc.</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-4">
                      <h2 className="text-xl font-bold text-gray-900 mb-4">Core Components</h2>
                      <div className="space-y-4">
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <CpuChipIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Classification Engine</h3>
                            <p className="text-gray-600 text-sm">Gemini-powered request analysis and routing decisions</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <CircleStackIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Semantic Cache</h3>
                            <p className="text-gray-600 text-sm">RouKey embeddings with reranking for intelligent caching</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <BoltIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">RouKey Orchestration</h3>
                            <p className="text-gray-600 text-sm">Multi-role workflow management and coordination</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <ChartBarIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Analytics Engine</h3>
                            <p className="text-gray-600 text-sm">Real-time monitoring and performance optimization</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="card p-4">
                      <h2 className="text-xl font-bold text-gray-900 mb-4">Infrastructure</h2>
                      <div className="space-y-4">
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <CloudIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Vercel Edge Network</h3>
                            <p className="text-gray-600 text-sm">Global edge deployment for minimal latency</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-teal-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <CircleStackIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Supabase Database</h3>
                            <p className="text-gray-600 text-sm">PostgreSQL with real-time subscriptions</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <ShieldCheckIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Security Layer</h3>
                            <p className="text-gray-600 text-sm">AES-256 encryption and secure key management</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <BoltIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Load Balancing</h3>
                            <p className="text-gray-600 text-sm">Intelligent request distribution and failover</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'getting-started' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">
                      Getting Started with RouKey
                    </h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Get up and running with RouKey in under 2 minutes. Follow our step-by-step guides to integrate RouKey into your application.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-4 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <BoltIcon className="h-5 w-5 text-[#ff6b35]" />
                        Quickstart
                      </h3>
                      <p className="text-gray-600 mb-4">Get RouKey running in 2 minutes with our fastest setup guide</p>
                      <button
                        onClick={() => setActiveSection('getting-started-quickstart')}
                        className="text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors"
                      >
                        Start now <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 p-4 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CogIcon className="h-5 w-5 text-blue-600" />
                        Installation
                      </h3>
                      <p className="text-gray-600 mb-4">Detailed installation and setup instructions</p>
                      <button
                        onClick={() => setActiveSection('getting-started-installation')}
                        className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        View guide <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-green-100/50 p-4 rounded-xl border border-green-200 hover:shadow-lg hover:border-green-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <PlayIcon className="h-5 w-5 text-green-600" />
                        First Request
                      </h3>
                      <p className="text-gray-600 mb-4">Make your first API call and see RouKey in action</p>
                      <button
                        onClick={() => setActiveSection('getting-started-first-request')}
                        className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Try it out <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 p-4 rounded-xl border border-purple-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CogIcon className="h-5 w-5 text-purple-600" />
                        Basic Setup
                      </h3>
                      <p className="text-gray-600 mb-4">Configure routing strategies and optimize your setup</p>
                      <button
                        onClick={() => setActiveSection('getting-started-basic-setup')}
                        className="text-purple-600 hover:text-purple-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Configure <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-6 rounded-xl text-white">
                    <h3 className="text-lg font-semibold mb-3">Need help getting started?</h3>
                    <p className="text-white/90 mb-4">
                      Our quickstart guide will have you up and running in under 2 minutes with your first RouKey API call.
                    </p>
                    <button
                      onClick={() => setActiveSection('getting-started-quickstart')}
                      className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                    >
                      Start Quickstart Guide
                    </button>
                  </div>
                </div>
              )}

              {/* Getting Started Subsections */}
              {activeSection === 'getting-started-quickstart' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">Quickstart Guide</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Get RouKey up and running in under 2 minutes with this step-by-step guide.
                    </p>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-xl border border-blue-200 mb-6">
                    <div className="flex items-center gap-3 mb-3">
                      <InformationCircleIcon className="h-6 w-6 text-blue-600" />
                      <h3 className="text-lg font-semibold text-blue-900">Prerequisites</h3>
                    </div>
                    <ul className="space-y-2 text-blue-800">
                      <li>• API keys from at least one LLM provider (OpenAI, Anthropic, etc.)</li>
                      <li>• A RouKey account (sign up at <a href="https://roukey.online" className="underline">roukey.online</a>)</li>
                      <li>• Basic knowledge of REST APIs</li>
                    </ul>
                  </div>

                  <div className="space-y-4">
                    <div className="card p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-7 h-7 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                        <h2 className="text-xl font-bold text-gray-900">Create Your Account</h2>
                      </div>
                      <p className="text-gray-600 mb-4">
                        Sign up for a free RouKey account to get started with intelligent AI routing.
                      </p>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-sm text-gray-600 mb-2">Visit:</p>
                        <a href="https://roukey.online" className="text-[#ff6b35] hover:text-[#e55a2b] font-medium underline">
                          https://roukey.online
                        </a>
                      </div>
                    </div>

                    <div className="card p-8">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-7 h-7 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                        <h2 className="text-xl font-bold text-gray-900">Add Provider API Keys</h2>
                      </div>
                      <p className="text-gray-600 mb-4">
                        Add your LLM provider API keys to enable RouKey's intelligent routing.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2">Supported Providers:</h4>
                          <ul className="text-sm text-gray-600 space-y-1">
                            <li>• OpenAI</li>
                            <li>• Anthropic</li>
                            <li>• Google</li>
                            <li>• DeepSeek</li>
                            <li>• xAI</li>
                            <li>• OpenRouter</li>
                          </ul>
                        </div>
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <h4 className="font-medium text-gray-900 mb-2">Security:</h4>
                          <p className="text-sm text-gray-600">
                            Your keys are encrypted with AES-256 and never leave your control.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="card p-8">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-7 h-7 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                        <h2 className="text-xl font-bold text-gray-900">Generate Your RouKey API Key</h2>
                      </div>
                      <p className="text-gray-600 mb-4">
                        Create a user API key to access RouKey's intelligent routing from your application.
                      </p>
                      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                        <p className="text-sm text-yellow-800">
                          <strong>Important:</strong> Your API key will be displayed only once. Make sure to copy and store it securely.
                        </p>
                      </div>
                    </div>

                    <div className="card p-8">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-7 h-7 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">4</div>
                        <h2 className="text-xl font-bold text-gray-900">Make Your First Request</h2>
                      </div>
                      <p className="text-gray-600 mb-6">
                        Test your setup with a simple API call to RouKey's chat completions endpoint.
                      </p>
                      <CodeBlock title="Your first RouKey API call" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Hello RouKey! How does intelligent routing work?"}
    ],
    "stream": false
  }'`}
                      </CodeBlock>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-xl text-white">
                    <h3 className="text-lg font-semibold mb-3">🎉 Congratulations!</h3>
                    <p className="text-white/90 mb-4">
                      You've successfully set up RouKey and made your first API call. RouKey is now intelligently routing your requests to optimize cost and performance.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <button
                        onClick={() => setActiveSection('getting-started-basic-setup')}
                        className="bg-white text-green-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                      >
                        Configure Routing Strategies
                      </button>
                      <button
                        onClick={() => setActiveSection('api-reference')}
                        className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors"
                      >
                        Explore API Reference
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'getting-started-installation' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">Installation Guide</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Detailed installation and setup instructions for integrating RouKey into your application.
                    </p>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">SDK Installation</h2>
                    <p className="text-gray-600 mb-6">
                      RouKey is compatible with existing OpenAI SDKs. Simply change the base URL to start using RouKey's intelligent routing.
                    </p>

                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">JavaScript/Node.js</h3>
                        <CodeBlock title="Install OpenAI SDK" language="bash">
{`npm install openai`}
                        </CodeBlock>
                        <div className="mt-4">
                          <CodeBlock title="Configure for RouKey" language="javascript">
{`import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: 'rk_live_your_api_key_here',
  baseURL: 'https://roukey.online/api/external/v1',
  defaultHeaders: {
    'X-API-Key': 'rk_live_your_api_key_here'
  }
});

// Use exactly like OpenAI
const completion = await openai.chat.completions.create({
  messages: [{ role: 'user', content: 'Hello RouKey!' }],
  model: 'gpt-4', // RouKey will intelligently route this
});`}
                          </CodeBlock>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Python</h3>
                        <CodeBlock title="Install OpenAI SDK" language="bash">
{`pip install openai`}
                        </CodeBlock>
                        <div className="mt-4">
                          <CodeBlock title="Configure for RouKey" language="python">
{`from openai import OpenAI

client = OpenAI(
    api_key="rk_live_your_api_key_here",
    base_url="https://roukey.online/api/external/v1",
    default_headers={"X-API-Key": "rk_live_your_api_key_here"}
)

# Use exactly like OpenAI
completion = client.chat.completions.create(
    messages=[{"role": "user", "content": "Hello RouKey!"}],
    model="gpt-4"  # RouKey will intelligently route this
)`}
                          </CodeBlock>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'getting-started-first-request' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Your First Request</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Learn how to make your first API call to RouKey and understand the response format.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Basic Request</h2>
                    <p className="text-gray-600 mb-6">
                      Here's a simple example of making your first request to RouKey's chat completions endpoint:
                    </p>
                    <CodeBlock title="Basic chat completion request" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Explain quantum computing in simple terms"}
    ],
    "stream": false,
    "temperature": 0.7
  }'`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Understanding the Response</h2>
                    <p className="text-gray-600 mb-6">
                      RouKey returns OpenAI-compatible responses with additional metadata about routing decisions:
                    </p>
                    <CodeBlock title="Example response" language="json">
{`{
  "id": "chatcmpl-abc123",
  "object": "chat.completion",
  "created": **********,
  "model": "routed-model",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Quantum computing is like having a super-powered calculator..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 12,
    "completion_tokens": 150,
    "total_tokens": 162
  },
  "roukey_metadata": {
    "routing_strategy": "intelligent_role",
    "selected_provider": "openai",
    "complexity_score": 3,
    "estimated_cost": 0.0024
  }
}`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Request with Role-Based Routing</h2>
                    <p className="text-gray-600 mb-6">
                      Use RouKey's role parameter to optimize routing for specific tasks:
                    </p>
                    <CodeBlock title="Role-based routing request" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}
    ],
    "role": "coding",
    "stream": true,
    "max_tokens": 1000
  }'`}
                    </CodeBlock>
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mt-4">
                      <p className="text-blue-800 text-sm">
                        <strong>Tip:</strong> Using <code>role: "coding"</code> will route your request to models optimized for code generation like Deepseek R1 0528.
                      </p>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Streaming Responses</h2>
                    <p className="text-gray-600 mb-6">
                      For better user experience and to avoid timeouts, use streaming responses:
                    </p>
                    <CodeBlock title="JavaScript streaming example" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [{ role: 'user', content: 'Write a story about AI' }],
    stream: true,
    role: 'writing'
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  const chunk = decoder.decode(value);
  const lines = chunk.split('\\n');

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') return;

      try {
        const parsed = JSON.parse(data);
        const content = parsed.choices[0]?.delta?.content;
        if (content) {
          console.log(content); // Stream the content
        }
      } catch (e) {
        // Handle parsing errors
      }
    }
  }
}`}
                    </CodeBlock>
                  </div>
                </div>
              )}

              {activeSection === 'getting-started-basic-setup' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Basic Setup</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Configure RouKey's routing strategies and optimize your setup for your specific use case.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Choosing a Routing Strategy</h2>
                    <p className="text-gray-600 mb-6">
                      RouKey offers several routing strategies. Choose the one that best fits your use case:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <BoltIcon className="h-5 w-5 text-blue-600" />
                          Intelligent Role Routing
                        </h3>
                        <p className="text-gray-600 mb-4">AI-powered classification routes requests based on detected roles (coding, writing, analysis, etc.) to specialized models.</p>
                        <div className="text-sm text-blue-600 font-medium">Best for: Multi-purpose applications</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <CircleStackIcon className="h-5 w-5 text-green-600" />
                          Complexity-Based Routing
                        </h3>
                        <p className="text-gray-600 mb-4">Analyzes prompt complexity (1-5 scale) and routes to appropriate models for optimal cost-performance balance.</p>
                        <div className="text-sm text-green-600 font-medium">Best for: Cost optimization</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <ListBulletIcon className="h-5 w-5 text-[#ff6b35]" />
                          Strict Fallback Strategy
                        </h3>
                        <p className="text-gray-600 mb-4">Ordered failover sequence with predictable routing and guaranteed fallback chain for maximum reliability.</p>
                        <div className="text-sm text-[#ff6b35] font-medium">Best for: Mission-critical applications</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <SparklesIcon className="h-5 w-5 text-purple-600" />
                          Cost-Optimized Routing
                        </h3>
                        <p className="text-gray-600 mb-4">Smart cost optimization with learning algorithms that adapt to your usage patterns over time.</p>
                        <div className="text-sm text-purple-600 font-medium">Best for: Budget-conscious deployments</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Custom Roles Configuration</h2>
                    <p className="text-gray-600 mb-6">
                      Create custom roles to optimize routing for your specific use cases:
                    </p>
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">Example: Custom "Data Analysis" Role</h4>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Primary: Claude 4 Opus (excellent for analysis)</li>
                          <li>• Fallback: Gemini 2.5 Pro (reliable backup)</li>
                          <li>• Cost-effective: GPT-4 (for simple queries)</li>
                        </ul>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">Example: Custom "Code Review" Role</h4>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Primary: GPT o3 (excellent for coding)</li>
                          <li>• Fallback: Deepseek R1 0528 (specialized for code)</li>
                          <li>• Fast: GPT-4 (for simple reviews)</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white">
                    <h3 className="text-xl font-semibold mb-4">Ready to optimize further?</h3>
                    <p className="text-white/90 mb-6">
                      Explore advanced features like semantic caching, knowledge base integration, and multi-role orchestration.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <button
                        onClick={() => setActiveSection('features')}
                        className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                      >
                        Explore Features
                      </button>
                      <button
                        onClick={() => setActiveSection('routing-strategies')}
                        className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors"
                      >
                        Advanced Routing
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'features' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">
                      RouKey Features
                    </h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Discover the comprehensive features that make RouKey the leading AI gateway solution for developers and enterprises.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 p-4 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <BoltIcon className="h-5 w-5 text-blue-600" />
                        Intelligent Routing
                      </h3>
                      <p className="text-gray-600 mb-4">AI-powered request classification and optimal model selection</p>
                      <button
                        onClick={() => setActiveSection('features-intelligent-routing')}
                        className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Learn more <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 p-4 rounded-xl border border-purple-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <UserGroupIcon className="h-5 w-5 text-purple-600" />
                        Multi-Role Orchestration
                      </h3>
                      <p className="text-gray-600 mb-4">Advanced workflow management with RouKey integration</p>
                      <button
                        onClick={() => setActiveSection('features-multi-role-orchestration')}
                        className="text-purple-600 hover:text-purple-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Explore workflows <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-green-100/50 p-4 rounded-xl border border-green-200 hover:shadow-lg hover:border-green-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CircleStackIcon className="h-5 w-5 text-green-600" />
                        Semantic Caching
                      </h3>
                      <p className="text-gray-600 mb-4">Intelligent caching with embeddings and reranking</p>
                      <button
                        onClick={() => setActiveSection('features-semantic-caching')}
                        className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        See caching <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-4 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <BookOpenIcon className="h-5 w-5 text-[#ff6b35]" />
                        Knowledge Base
                      </h3>
                      <p className="text-gray-600 mb-4">Upload documents for enhanced AI responses</p>
                      <button
                        onClick={() => setActiveSection('features-knowledge-base')}
                        className="text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors"
                      >
                        Upload docs <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-indigo-50 to-indigo-100/50 p-4 rounded-xl border border-indigo-200 hover:shadow-lg hover:border-indigo-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CogIcon className="h-5 w-5 text-indigo-600" />
                        Custom Training
                      </h3>
                      <p className="text-gray-600 mb-4">Train models with your specific data and requirements</p>
                      <button
                        onClick={() => setActiveSection('features-custom-training')}
                        className="text-indigo-600 hover:text-indigo-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Start training <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-teal-50 to-teal-100/50 p-4 rounded-xl border border-teal-200 hover:shadow-lg hover:border-teal-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <BoltIcon className="h-5 w-5 text-teal-600" />
                        Async Processing
                      </h3>
                      <p className="text-gray-600 mb-4">Handle long-running tasks with webhook notifications</p>
                      <button
                        onClick={() => setActiveSection('features-async-processing')}
                        className="text-teal-600 hover:text-teal-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Process async <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100/50 p-4 rounded-xl border border-yellow-200 hover:shadow-lg hover:border-yellow-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <ChartBarIcon className="h-5 w-5 text-yellow-600" />
                        Analytics & Monitoring
                      </h3>
                      <p className="text-gray-600 mb-4">Real-time insights and performance monitoring</p>
                      <button
                        onClick={() => setActiveSection('features-analytics-monitoring')}
                        className="text-yellow-600 hover:text-yellow-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        View analytics <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Multi-Role Orchestration</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey's advanced orchestration integration enables sophisticated multi-role task orchestration with automatic workflow detection and management.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Workflow Types</h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• <strong>Sequential:</strong> Single roles, step-by-step processing</li>
                            <li>• <strong>Supervisor:</strong> 2-3 roles with coordinated execution</li>
                            <li>• <strong>Hierarchical:</strong> 4+ roles or complex browsing tasks</li>
                            <li>• <strong>Auto:</strong> Intelligent workflow selection</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Advanced Features</h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• <strong>Memory Management:</strong> Context preservation across roles</li>
                            <li>• <strong>Streaming Support:</strong> Real-time progress updates</li>
                            <li>• <strong>Error Recovery:</strong> Automatic retry and fallback</li>
                            <li>• <strong>Progress Tracking:</strong> Detailed execution monitoring</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Provider Support</h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">OpenAI</div>
                        <div className="text-xs text-gray-600">GPT-4, GPT o3, o1</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Anthropic</div>
                        <div className="text-xs text-gray-600">Claude 4 Opus, Claude 3</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Google</div>
                        <div className="text-xs text-gray-600">Gemini 2.5 Pro, Flash</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">DeepSeek</div>
                        <div className="text-xs text-gray-600">v3, R1 0528</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">xAI</div>
                        <div className="text-xs text-gray-600">Grok Models</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">OpenRouter</div>
                        <div className="text-xs text-gray-600">300+ Models</div>
                      </div>
                    </div>
                    <p className="text-gray-600 mt-6 text-center">
                      Access to <strong>300+ AI models</strong> from leading providers with unified API interface
                    </p>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Advanced Capabilities</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">RouKey Semantic Caching</h3>
                        <p className="text-gray-600 mb-4">
                          RouKey's intelligent caching system uses advanced semantic analysis and reranking for faster responses and reduced costs.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• RouKey embedding-based similarity detection</li>
                          <li>• Advanced reranking for result optimization</li>
                          <li>• Automatic cache invalidation</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Knowledge Base Integration</h3>
                        <p className="text-gray-600 mb-4">
                          Upload and integrate custom documents for enhanced AI responses with your specific knowledge.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Document upload and processing</li>
                          <li>• Automatic embedding generation</li>
                          <li>• Context-aware retrieval</li>
                          <li>• Tier-based document limits</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Custom Training</h3>
                        <p className="text-gray-600 mb-4">
                          Train models with your specific data and requirements for specialized use cases.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• File upload and processing</li>
                          <li>• Training job management</li>
                          <li>• Enhanced system prompts</li>
                          <li>• Multi-provider compatibility</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Async Processing</h3>
                        <p className="text-gray-600 mb-4">
                          Handle long-running tasks with asynchronous processing and webhook notifications.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Job submission and tracking</li>
                          <li>• Webhook notifications</li>
                          <li>• Progress monitoring</li>
                          <li>• Result retrieval</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Features Subsections */}
              {activeSection === 'features-intelligent-routing' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">Intelligent Routing</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      RouKey's AI-powered routing engine automatically selects the optimal model for each request based on multiple factors.
                    </p>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Routing Strategies</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <BoltIcon className="h-5 w-5 text-blue-600" />
                          Intelligent Role Routing
                        </h3>
                        <p className="text-gray-600 mb-4">AI-powered classification routes requests based on detected roles (coding, writing, analysis, etc.) to specialized models.</p>
                        <div className="text-sm text-blue-600 font-medium">Best for: Multi-purpose applications</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <CircleStackIcon className="h-5 w-5 text-green-600" />
                          Complexity-Based Routing
                        </h3>
                        <p className="text-gray-600 mb-4">Analyzes prompt complexity (1-5 scale) and routes to appropriate models for optimal cost-performance balance.</p>
                        <div className="text-sm text-green-600 font-medium">Best for: Cost optimization</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <ListBulletIcon className="h-5 w-5 text-[#ff6b35]" />
                          Strict Fallback Strategy
                        </h3>
                        <p className="text-gray-600 mb-4">Ordered failover sequence with predictable routing and guaranteed fallback chain for maximum reliability.</p>
                        <div className="text-sm text-[#ff6b35] font-medium">Best for: Mission-critical applications</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <SparklesIcon className="h-5 w-5 text-purple-600" />
                          Cost-Optimized Routing
                        </h3>
                        <p className="text-gray-600 mb-4">Smart cost optimization with learning algorithms that adapt to your usage patterns over time.</p>
                        <div className="text-sm text-purple-600 font-medium">Best for: Budget-conscious deployments</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">How Routing Works</h2>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                          <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span className="text-white font-bold text-xl">1</span>
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-3">Request Analysis</h3>
                          <p className="text-gray-600 text-sm">Gemini-powered classification analyzes request content, complexity, and context</p>
                        </div>
                        <div className="text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                          <div className="w-16 h-16 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span className="text-white font-bold text-xl">2</span>
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-3">Model Selection</h3>
                          <p className="text-gray-600 text-sm">Algorithm selects optimal model based on strategy, availability, and cost</p>
                        </div>
                        <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                          <div className="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span className="text-white font-bold text-xl">3</span>
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-3">Request Routing</h3>
                          <p className="text-gray-600 text-sm">Request is routed to selected model with automatic failover if needed</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Configuration Example</h2>
                    <p className="text-gray-600 mb-6">
                      Here's how to configure intelligent routing step by step:
                    </p>

                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Step 1: Create a Custom Configuration</h3>
                        <CodeBlock title="Create custom configuration" language="bash">
{`POST /api/custom-configs
Content-Type: application/json

{
  "name": "My Coding Assistant"
}`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Step 2: Add API Keys for Different Models</h3>
                        <CodeBlock title="Add API keys" language="bash">
{`# Add DeepSeek key for coding
POST /api/keys
Content-Type: application/json

{
  "custom_api_config_id": "your-config-id",
  "provider": "deepseek",
  "predefined_model_id": "deepseek-coder",
  "api_key_raw": "your-deepseek-api-key",
  "label": "DeepSeek Coder",
  "temperature": 0.1
}

# Add Claude key for writing
POST /api/keys
Content-Type: application/json

{
  "custom_api_config_id": "your-config-id",
  "provider": "anthropic",
  "predefined_model_id": "claude-3-5-sonnet",
  "api_key_raw": "your-claude-api-key",
  "label": "Claude Writer",
  "temperature": 0.7
}

# Add GPT-4 key for general chat (set as default)
POST /api/keys
Content-Type: application/json

{
  "custom_api_config_id": "your-config-id",
  "provider": "openai",
  "predefined_model_id": "gpt-4",
  "api_key_raw": "your-openai-api-key",
  "label": "GPT-4 General",
  "temperature": 1.0
}`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Step 3: Assign Roles to API Keys</h3>
                        <CodeBlock title="Assign roles" language="bash">
{`# Assign coding role to DeepSeek key
POST /api/keys/{deepseek-key-id}/roles
Content-Type: application/json

{
  "role_name": "coding_backend"
}

# Assign writing role to Claude key
POST /api/keys/{claude-key-id}/roles
Content-Type: application/json

{
  "role_name": "writing"
}

# Set GPT-4 as default general chat model
PUT /api/custom-configs/{config-id}/default-key-handler/{gpt4-key-id}`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Step 4: Enable Intelligent Role Routing</h3>
                        <CodeBlock title="Enable intelligent routing" language="bash">
{`PUT /api/custom-configs/{config-id}/routing
Content-Type: application/json

{
  "routing_strategy": "intelligent_role",
  "routing_strategy_params": null
}`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">How It Works</h3>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-gray-700 mb-3">
                            Once configured, RouKey automatically:
                          </p>
                          <ul className="list-disc list-inside space-y-2 text-gray-700">
                            <li><strong>Classifies incoming prompts</strong> using advanced AI to determine the task type (coding, writing, analysis, etc.)</li>
                            <li><strong>Routes to the assigned model</strong> for that specific role based on your role assignments</li>
                            <li><strong>Falls back to the default general chat model</strong> if no specific role is assigned or detected</li>
                            <li><strong>Provides seamless switching</strong> between different specialized models within the same conversation</li>
                          </ul>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Available Predefined Roles</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-blue-50 p-3 rounded-lg">
                            <h4 className="font-semibold text-blue-900 mb-2">Development</h4>
                            <ul className="text-sm text-blue-800 space-y-1">
                              <li>• <code>coding_frontend</code> - HTML, CSS, JavaScript, React</li>
                              <li>• <code>coding_backend</code> - APIs, databases, server-side</li>
                              <li>• <code>data_extraction_structuring</code> - Data processing</li>
                            </ul>
                          </div>
                          <div className="bg-green-50 p-3 rounded-lg">
                            <h4 className="font-semibold text-green-900 mb-2">Content & Analysis</h4>
                            <ul className="text-sm text-green-800 space-y-1">
                              <li>• <code>writing</code> - Articles, blogs, creative content</li>
                              <li>• <code>research_synthesis</code> - Research and analysis</li>
                              <li>• <code>summarization_briefing</code> - Document summaries</li>
                            </ul>
                          </div>
                          <div className="bg-purple-50 p-3 rounded-lg">
                            <h4 className="font-semibold text-purple-900 mb-2">Specialized</h4>
                            <ul className="text-sm text-purple-800 space-y-1">
                              <li>• <code>logic_reasoning</code> - Math, logical puzzles</li>
                              <li>• <code>translation_localization</code> - Language translation</li>
                              <li>• <code>general_chat</code> - Casual conversation</li>
                            </ul>
                          </div>
                          <div className="bg-orange-50 p-3 rounded-lg">
                            <h4 className="font-semibold text-orange-900 mb-2">Custom Roles</h4>
                            <p className="text-sm text-orange-800">
                              Create your own custom roles for specialized use cases specific to your application.
                            </p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Step 5: Use Your Configured Routing</h3>
                        <CodeBlock title="Make requests with intelligent routing" language="bash">
{`# All requests now automatically use intelligent routing
POST /api/v1/chat/completions
Content-Type: application/json

{
  "custom_api_config_id": "your-config-id",
  "messages": [
    {
      "role": "user",
      "content": "Write a Python function to calculate fibonacci numbers"
    }
  ]
}

# RouKey will:
# 1. Classify this as a coding task
# 2. Route to your DeepSeek key (assigned to coding_backend role)
# 3. Return the response from DeepSeek Coder

# For a writing task:
POST /api/v1/chat/completions
Content-Type: application/json

{
  "custom_api_config_id": "your-config-id",
  "messages": [
    {
      "role": "user",
      "content": "Write a blog post about artificial intelligence"
    }
  ]
}

# RouKey will:
# 1. Classify this as a writing task
# 2. Route to your Claude key (assigned to writing role)
# 3. Return the response from Claude`}
                        </CodeBlock>
                      </div>

                      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                        <h4 className="font-semibold text-amber-900 mb-2">📋 Important Notes</h4>
                        <ul className="text-sm text-amber-800 space-y-1">
                          <li>• <strong>Subscription Required:</strong> Intelligent role routing is available on Starter, Professional, and Enterprise plans</li>
                          <li>• <strong>Role Management:</strong> Custom role creation and assignment requires a paid plan</li>
                          <li>• <strong>API Key Limits:</strong> Free tier is limited to 3 API keys maximum</li>
                          <li>• <strong>Automatic Classification:</strong> RouKey uses advanced AI to classify prompts - no manual role specification needed</li>
                          <li>• <strong>Fallback Behavior:</strong> If no role is assigned for a classified task, requests fall back to your default general chat model</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'features-semantic-caching' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">Semantic Caching</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      RouKey's intelligent caching system uses advanced semantic analysis to reduce costs and improve response times.
                    </p>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">How It Works</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Unlike traditional caching that requires exact matches, RouKey's semantic caching understands the meaning of requests and can serve cached responses for semantically similar queries.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CircleStackIcon className="h-5 w-5 text-blue-600" />
                            RouKey Embeddings
                          </h3>
                          <p className="text-gray-600 mb-4">
                            Requests are converted to high-dimensional embeddings that capture semantic meaning.
                          </p>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Sub-second embedding generation</li>
                            <li>• Multi-language support</li>
                            <li>• High-performance processing</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <SparklesIcon className="h-5 w-5 text-green-600" />
                            Reranking
                          </h3>
                          <p className="text-gray-600 mb-4">
                            RouKey reranker optimizes cache hit accuracy by reordering similarity results.
                          </p>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Improved relevance scoring</li>
                            <li>• Context-aware matching</li>
                            <li>• Reduced false positives</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Cache Performance</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <div className="text-3xl font-bold text-green-600 mb-2">85%</div>
                        <div className="text-gray-900 font-medium mb-1">Cache Hit Rate</div>
                        <div className="text-gray-600 text-sm">Average across all request types</div>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <div className="text-3xl font-bold text-blue-600 mb-2">50ms</div>
                        <div className="text-gray-900 font-medium mb-1">Response Time</div>
                        <div className="text-gray-600 text-sm">Average for cached responses</div>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <div className="text-3xl font-bold text-[#ff6b35] mb-2">90%</div>
                        <div className="text-gray-900 font-medium mb-1">Cost Reduction</div>
                        <div className="text-gray-600 text-sm">For cached requests</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Example Scenarios</h2>
                    <div className="space-y-6">
                      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                        <h4 className="font-medium text-gray-900 mb-3">Scenario: Similar Coding Questions</h4>
                        <div className="space-y-2">
                          <div className="p-3 bg-white rounded border-l-4 border-blue-500">
                            <div className="text-sm text-gray-600 mb-1">Original Request:</div>
                            <div className="text-gray-900">"Write a Python function to sort a list"</div>
                          </div>
                          <div className="p-3 bg-green-50 rounded border-l-4 border-green-500">
                            <div className="text-sm text-gray-600 mb-1">Cache Hit:</div>
                            <div className="text-gray-900">"Create a Python function for sorting an array"</div>
                          </div>
                        </div>
                        <div className="mt-4 text-sm text-gray-600">
                          <strong>Result:</strong> 95% similarity match, cached response served in 45ms
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Multi-Role Orchestration Section */}
              {activeSection === 'features-multi-role-orchestration' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">Multi-Role Orchestration</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      RouKey's advanced orchestration system automatically detects when tasks require multiple specialized AI roles working together, then coordinates their execution using intelligent workflow patterns for optimal results.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Workflow Types</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ArrowRightIcon className="h-5 w-5 text-blue-600" />
                          Sequential Workflow
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Single role execution for focused, domain-specific tasks.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• One specialized role handles the task</li>
                          <li>• Direct, efficient processing</li>
                          <li>• Fastest execution time</li>
                          <li>• Best for single-domain requests</li>
                        </ul>
                        <div className="mt-4 p-3 bg-blue-100 rounded-lg">
                          <p className="text-xs text-blue-800 font-medium">Example: "Write a Python function to sort a list"</p>
                        </div>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <UserGroupIcon className="h-5 w-5 text-green-600" />
                          Supervisor Workflow
                        </h3>
                        <p className="text-gray-600 mb-4">
                          2-4 roles with coordinated execution and intelligent supervision.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Multiple roles work sequentially</li>
                          <li>• Each role builds on previous output</li>
                          <li>• Intelligent handoff coordination</li>
                          <li>• Quality validation between steps</li>
                        </ul>
                        <div className="mt-4 p-3 bg-green-100 rounded-lg">
                          <p className="text-xs text-green-800 font-medium">Example: "Research competitors, analyze data, create presentation"</p>
                        </div>
                      </div>
                      <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <BuildingOfficeIcon className="h-5 w-5 text-purple-600" />
                          Hierarchical Workflow
                        </h3>
                        <p className="text-gray-600 mb-4">
                          5+ roles for complex, multi-domain tasks requiring specialized coordination.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Complex task decomposition</li>
                          <li>• Multi-level role coordination</li>
                          <li>• Advanced dependency management</li>
                          <li>• Comprehensive result synthesis</li>
                        </ul>
                        <div className="mt-4 p-3 bg-purple-100 rounded-lg">
                          <p className="text-xs text-purple-800 font-medium">Example: "Build full-stack app with docs, tests, deployment"</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Intelligent Detection & Examples</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey uses advanced AI classification to automatically detect when multi-role orchestration is needed and selects the appropriate workflow type based on task complexity and requirements.
                      </p>

                      <div className="bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Detection Process</h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">1</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Request Analysis</div>
                            <div className="text-sm text-gray-600">Analyze complexity and domain requirements</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">2</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Role Matching</div>
                            <div className="text-sm text-gray-600">Match available roles to task requirements</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">3</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Workflow Selection</div>
                            <div className="text-sm text-gray-600">Choose optimal orchestration pattern</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">4</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Execution</div>
                            <div className="text-sm text-gray-600">Coordinate roles with real-time streaming</div>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Single-Role Detection</h3>
                          <p className="text-gray-600 mb-4">Tasks that can be handled by one specialized role:</p>
                          <ul className="space-y-2 text-gray-600 text-sm">
                            <li>• "Debug this Python code"</li>
                            <li>• "Write a marketing email"</li>
                            <li>• "Explain quantum computing"</li>
                            <li>• "Create a SQL query"</li>
                          </ul>
                          <div className="mt-4 p-3 bg-blue-100 rounded-lg">
                            <p className="text-xs text-blue-800"><strong>Result:</strong> Sequential workflow with 1 role</p>
                          </div>
                        </div>
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Multi-Role Detection</h3>
                          <p className="text-gray-600 mb-4">Complex tasks requiring multiple specializations:</p>
                          <ul className="space-y-2 text-gray-600 text-sm">
                            <li>• "Research market trends, analyze data, create strategy"</li>
                            <li>• "Build API, write tests, create documentation"</li>
                            <li>• "Design UI, implement frontend, optimize performance"</li>
                            <li>• "Analyze competitors, plan features, estimate costs"</li>
                          </ul>
                          <div className="mt-4 p-3 bg-green-100 rounded-lg">
                            <p className="text-xs text-green-800"><strong>Result:</strong> Supervisor/Hierarchical workflow with 2-5+ roles</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Automatic Streaming & Real-Time Coordination</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey automatically enables streaming for multi-role tasks, providing real-time progress updates and seamless coordination between AI roles.
                      </p>

                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Streaming Features</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                              Auto-Detection
                            </h4>
                            <ul className="text-gray-600 text-sm space-y-1">
                              <li>• Automatic stream=true for multi-role</li>
                              <li>• No manual configuration needed</li>
                              <li>• Intelligent workflow selection</li>
                              <li>• Seamless API integration</li>
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                              Real-Time Updates
                            </h4>
                            <ul className="text-gray-600 text-sm space-y-1">
                              <li>• Live role execution progress</li>
                              <li>• Server-Sent Events (SSE)</li>
                              <li>• Role handoff notifications</li>
                              <li>• Step-by-step visibility</li>
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                              <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                              Performance
                            </h4>
                            <ul className="text-gray-600 text-sm space-y-1">
                              <li>• No 60-second timeout limits</li>
                              <li>• Prevents Vercel timeouts</li>
                              <li>• Chunked response delivery</li>
                              <li>• Optimized for complex tasks</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-xl border border-green-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">API Streaming Behavior</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">External API</h4>
                            <p className="text-gray-600 text-sm mb-3">
                              When multi-role tasks are detected via RouKey's AI classification, streaming is automatically forced:
                            </p>
                            <div className="bg-white p-3 rounded-lg border">
                              <code className="text-xs text-gray-800">
                                {`{
  "stream": true,
  "streaming_forced": true,
  "streaming_reason": "Multi-role orchestration detected"
}`}
                              </code>
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Internal Orchestration</h4>
                            <p className="text-gray-600 text-sm mb-3">
                              Real-time coordination uses Server-Sent Events for live updates:
                            </p>
                            <div className="bg-white p-3 rounded-lg border">
                              <code className="text-xs text-gray-800">
                                {`GET /api/orchestration/stream/{executionId}
Content-Type: text/event-stream
Cache-Control: no-cache`}
                              </code>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Real-World Examples</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        See how RouKey's multi-role orchestration handles complex tasks by coordinating specialized AI roles.
                      </p>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                            Software Development Project
                          </h3>
                          <div className="space-y-4">
                            <div className="bg-white p-4 rounded-lg border border-blue-100">
                              <p className="text-sm text-gray-700 font-medium mb-2">User Request:</p>
                              <p className="text-sm text-gray-600 italic">"Build a REST API for user management with authentication, write comprehensive tests, and create API documentation"</p>
                            </div>
                            <div className="space-y-3">
                              <div className="flex items-start gap-3">
                                <span className="w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center font-bold">1</span>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">Backend Developer Role</p>
                                  <p className="text-xs text-gray-600">Creates API endpoints, authentication logic, database models</p>
                                </div>
                              </div>
                              <div className="flex items-start gap-3">
                                <span className="w-6 h-6 bg-green-500 text-white text-xs rounded-full flex items-center justify-center font-bold">2</span>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">QA Engineer Role</p>
                                  <p className="text-xs text-gray-600">Writes unit tests, integration tests, validates functionality</p>
                                </div>
                              </div>
                              <div className="flex items-start gap-3">
                                <span className="w-6 h-6 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center font-bold">3</span>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">Technical Writer Role</p>
                                  <p className="text-xs text-gray-600">Creates API documentation, usage examples, deployment guide</p>
                                </div>
                              </div>
                            </div>
                            <div className="bg-green-100 p-3 rounded-lg border border-green-200">
                              <p className="text-xs text-green-800"><strong>Workflow:</strong> Supervisor (3 roles) with automatic streaming</p>
                            </div>
                          </div>
                        </div>

                        <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <span className="w-3 h-3 bg-green-500 rounded-full"></span>
                            Market Research & Strategy
                          </h3>
                          <div className="space-y-4">
                            <div className="bg-white p-4 rounded-lg border border-green-100">
                              <p className="text-sm text-gray-700 font-medium mb-2">User Request:</p>
                              <p className="text-sm text-gray-600 italic">"Research the AI tools market, analyze competitor pricing, and create a go-to-market strategy for our new product"</p>
                            </div>
                            <div className="space-y-3">
                              <div className="flex items-start gap-3">
                                <span className="w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center font-bold">1</span>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">Research Analyst Role</p>
                                  <p className="text-xs text-gray-600">Gathers market data, identifies key players, trends analysis</p>
                                </div>
                              </div>
                              <div className="flex items-start gap-3">
                                <span className="w-6 h-6 bg-green-500 text-white text-xs rounded-full flex items-center justify-center font-bold">2</span>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">Data Analyst Role</p>
                                  <p className="text-xs text-gray-600">Analyzes pricing models, market size, competitive positioning</p>
                                </div>
                              </div>
                              <div className="flex items-start gap-3">
                                <span className="w-6 h-6 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center font-bold">3</span>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">Strategy Consultant Role</p>
                                  <p className="text-xs text-gray-600">Creates go-to-market plan, pricing strategy, launch timeline</p>
                                </div>
                              </div>
                            </div>
                            <div className="bg-green-100 p-3 rounded-lg border border-green-200">
                              <p className="text-xs text-green-800"><strong>Workflow:</strong> Supervisor (3 roles) with real-time coordination</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <span className="w-3 h-3 bg-purple-500 rounded-full"></span>
                          Complex Enterprise Application (Hierarchical)
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <div className="bg-white p-4 rounded-lg border border-purple-100 mb-4">
                              <p className="text-sm text-gray-700 font-medium mb-2">User Request:</p>
                              <p className="text-sm text-gray-600 italic">"Build a complete e-commerce platform with frontend, backend, payment integration, admin dashboard, and mobile app"</p>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm">
                                <span className="w-5 h-5 bg-blue-500 text-white text-xs rounded flex items-center justify-center">1</span>
                                <span className="text-gray-900">System Architect</span>
                              </div>
                              <div className="flex items-center gap-2 text-sm">
                                <span className="w-5 h-5 bg-green-500 text-white text-xs rounded flex items-center justify-center">2</span>
                                <span className="text-gray-900">Backend Developer</span>
                              </div>
                              <div className="flex items-center gap-2 text-sm">
                                <span className="w-5 h-5 bg-purple-500 text-white text-xs rounded flex items-center justify-center">3</span>
                                <span className="text-gray-900">Frontend Developer</span>
                              </div>
                              <div className="flex items-center gap-2 text-sm">
                                <span className="w-5 h-5 bg-orange-500 text-white text-xs rounded flex items-center justify-center">4</span>
                                <span className="text-gray-900">Mobile Developer</span>
                              </div>
                              <div className="flex items-center gap-2 text-sm">
                                <span className="w-5 h-5 bg-red-500 text-white text-xs rounded flex items-center justify-center">5</span>
                                <span className="text-gray-900">DevOps Engineer</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-3">Hierarchical Coordination</h4>
                            <ul className="space-y-2 text-sm text-gray-600">
                              <li>• Multi-level task decomposition</li>
                              <li>• Parallel development streams</li>
                              <li>• Cross-role dependency management</li>
                              <li>• Integrated testing & deployment</li>
                              <li>• Real-time progress synchronization</li>
                            </ul>
                            <div className="bg-purple-100 p-3 rounded-lg border border-purple-200 mt-4">
                              <p className="text-xs text-purple-800"><strong>Workflow:</strong> Hierarchical (5+ roles) with advanced streaming</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Automatic Streaming:</strong> RouKey automatically detects multi-role tasks and enables streaming to provide real-time progress updates and prevent timeout issues. No manual configuration required.
                  </Alert>
                </div>
              )}

              {/* Knowledge Base Section */}
              {activeSection === 'features-knowledge-base' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Knowledge Base</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Enhance your AI responses with custom knowledge by uploading documents that provide domain-specific context and expertise.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Document Upload & Processing</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Upload documents in various formats to create a custom knowledge base that enhances AI responses with your proprietary information.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <DocumentIcon className="h-5 w-5 text-blue-600" />
                            Supported Formats
                          </h3>
                          <ul className="text-gray-600 space-y-2">
                            <li>• PDF documents</li>
                            <li>• Word documents (.docx)</li>
                            <li>• Text files (.txt)</li>
                            <li>• Markdown files (.md)</li>
                            <li>• CSV files</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CogIcon className="h-5 w-5 text-green-600" />
                            Processing Features
                          </h3>
                          <ul className="text-gray-600 space-y-2">
                            <li>• Automatic text extraction</li>
                            <li>• Intelligent chunking</li>
                            <li>• Semantic indexing</li>
                            <li>• Context preservation</li>
                            <li>• Metadata extraction</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Tier Limits & Usage</h2>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Tier</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Max Documents</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">File Size Limit</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Features</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Free</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">-</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Upgrade required</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Starter</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">-</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Upgrade required</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Professional</td>
                            <td className="px-6 py-4 text-sm text-gray-600">5 documents</td>
                            <td className="px-6 py-4 text-sm text-gray-600">10MB per file</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Full knowledge base access</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Enterprise</td>
                            <td className="px-6 py-4 text-sm text-gray-600">15 documents</td>
                            <td className="px-6 py-4 text-sm text-gray-600">25MB per file</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Advanced features, priority processing</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Intelligent Context Retrieval</h3>
                        <p className="text-gray-600 mb-4">
                          When you ask a question, RouKey automatically searches your knowledge base for relevant information and includes it in the AI's context.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">1</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Query Analysis</div>
                            <div className="text-sm text-gray-600">Understand user intent</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">2</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Document Search</div>
                            <div className="text-sm text-gray-600">Find relevant content</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">3</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Context Injection</div>
                            <div className="text-sm text-gray-600">Add to AI prompt</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">4</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Enhanced Response</div>
                            <div className="text-sm text-gray-600">AI responds with context</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Best Practice:</strong> Upload well-structured documents with clear headings and sections for optimal knowledge extraction and retrieval accuracy.
                  </Alert>
                </div>
              )}

              {/* Custom Training Section */}
              {activeSection === 'features-custom-training' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Custom Training</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Train AI models with your specific data, instructions, and behavioral patterns to create domain-specific assistants tailored to your needs.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Training Methods</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <DocumentTextIcon className="h-5 w-5 text-blue-600" />
                          Prompt Engineering
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Define AI behavior through custom system prompts, instructions, and example interactions.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• System instructions</li>
                          <li>• Behavioral guidelines</li>
                          <li>• Example conversations</li>
                          <li>• Response formatting</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <AcademicCapIcon className="h-5 w-5 text-green-600" />
                          Fine-tuning (Coming Soon)
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Advanced model customization with your specific datasets for specialized performance.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Custom model weights</li>
                          <li>• Domain specialization</li>
                          <li>• Performance optimization</li>
                          <li>• Proprietary knowledge</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Training Format Guide</h2>
                    <div className="space-y-6">
                      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Supported Formats</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">System Instructions</h4>
                            <div className="bg-white p-3 rounded border font-mono text-sm">
                              SYSTEM: You are a helpful customer service agent for our company<br/>
                              BEHAVIOR: Always be polite and offer solutions
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Example Interactions</h4>
                            <div className="bg-white p-3 rounded border font-mono text-sm">
                              User asks about returns → I'd be happy to help with your return!<br/>
                              Customer is frustrated → I understand your frustration. Let me help.
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">General Instructions</h4>
                            <div className="bg-white p-3 rounded border font-mono text-sm">
                              Always provide step-by-step explanations for technical topics.<br/>
                              Include relevant examples when explaining concepts.
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Tier Availability</h2>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Tier</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Prompt Engineering</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Fine-tuning</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Custom Models</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Free</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Starter</td>
                            <td className="px-6 py-4 text-sm text-green-600">✓ Available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Professional</td>
                            <td className="px-6 py-4 text-sm text-green-600">✓ Available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Enterprise</td>
                            <td className="px-6 py-4 text-sm text-green-600">✓ Available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <Alert type="info">
                    <strong>Getting Started:</strong> Custom training through prompt engineering is available for Starter tier and above. Visit the Training page in your dashboard to create custom prompts and behavioral instructions.
                  </Alert>
                </div>
              )}

              {/* Async Processing Section */}
              {activeSection === 'features-async-processing' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Async Processing</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Handle long-running AI tasks with webhook notifications, perfect for complex multi-role workflows and time-intensive operations.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Submit tasks for background processing and receive results via webhooks when complete, eliminating timeout constraints.
                      </p>
                      <div className="bg-gradient-to-r from-teal-50 to-teal-100 p-6 rounded-xl border border-teal-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Processing Flow</h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">1</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Submit Job</div>
                            <div className="text-sm text-gray-600">Send request with webhook URL</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">2</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Queue Processing</div>
                            <div className="text-sm text-gray-600">Task added to processing queue</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">3</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Background Execution</div>
                            <div className="text-sm text-gray-600">AI processes without timeout</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">4</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Webhook Notification</div>
                            <div className="text-sm text-gray-600">Results sent to your endpoint</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">API Usage</h2>
                    <div className="space-y-6">
                      <h3 className="text-xl font-semibold text-gray-900">Submit Async Job</h3>
                      <CodeBlock title="Submit async processing job" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/async/submit" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Analyze this complex dataset and provide insights"}
    ],
    "webhook_url": "https://your-app.com/webhook/roukey",
    "metadata": {
      "task_id": "analysis_001",
      "priority": "high"
    }
  }'`}
                      </CodeBlock>

                      <h3 className="text-xl font-semibold text-gray-900">Check Job Status</h3>
                      <CodeBlock title="Check async job status" language="bash">
{`curl -X GET "https://roukey.online/api/external/v1/async/status/job_id_here" \\
  -H "X-API-Key: rk_live_your_api_key_here"`}
                      </CodeBlock>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Webhook Integration</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Receive job completion notifications at your specified webhook endpoint with full results and metadata.
                      </p>
                      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Webhook Payload Example</h3>
                        <CodeBlock title="Webhook notification payload" language="json">
{`{
  "job_id": "async_job_123456",
  "status": "completed",
  "result": {
    "response": "Analysis complete: The dataset shows...",
    "roles_used": ["analyst", "researcher"],
    "processing_time": 45.2,
    "tokens_used": 2847
  },
  "metadata": {
    "task_id": "analysis_001",
    "priority": "high"
  },
  "completed_at": "2024-06-24T10:30:00Z"
}`}
                        </CodeBlock>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Best Use Cases:</strong> Async processing is ideal for complex multi-role tasks, large document analysis, research workflows, and any operation that might exceed standard timeout limits.
                  </Alert>
                </div>
              )}

              {/* Analytics & Monitoring Section */}
              {activeSection === 'features-analytics-monitoring' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Analytics & Monitoring</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Comprehensive real-time insights into your AI usage, performance metrics, and cost optimization opportunities.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Performance Metrics</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ClockIcon className="h-5 w-5 text-blue-600" />
                          Response Times
                        </h3>
                        <ul className="text-gray-600 space-y-2">
                          <li>• First token latency tracking</li>
                          <li>• Complete response timing</li>
                          <li>• Provider comparison</li>
                          <li>• Performance trends</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
                          Cost Analytics
                        </h3>
                        <ul className="text-gray-600 space-y-2">
                          <li>• Token usage tracking</li>
                          <li>• Cost per request</li>
                          <li>• Provider cost comparison</li>
                          <li>• Budget monitoring</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ChartBarIcon className="h-5 w-5 text-purple-600" />
                          Usage Statistics
                        </h3>
                        <ul className="text-gray-600 space-y-2">
                          <li>• Request volume trends</li>
                          <li>• Model usage distribution</li>
                          <li>• Cache hit rates</li>
                          <li>• Error rate monitoring</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Real-time Monitoring</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Monitor your AI infrastructure in real-time with comprehensive dashboards and alerting capabilities.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-yellow-50 rounded-xl border border-yellow-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                            Health Monitoring
                          </h3>
                          <ul className="text-gray-600 space-y-2">
                            <li>• API endpoint health checks</li>
                            <li>• Provider availability status</li>
                            <li>• Automatic failover tracking</li>
                            <li>• System uptime monitoring</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-red-50 rounded-xl border border-red-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <BellIcon className="h-5 w-5 text-red-600" />
                            Alert System
                          </h3>
                          <ul className="text-gray-600 space-y-2">
                            <li>• High error rate alerts</li>
                            <li>• Cost threshold notifications</li>
                            <li>• Performance degradation warnings</li>
                            <li>• Custom alert rules</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Performance Targets</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">First Token Performance</h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                            <div className="text-2xl font-bold text-green-600 mb-1">⚡</div>
                            <div className="font-medium text-gray-900 mb-1">Excellent</div>
                            <div className="text-sm text-gray-600">&lt; 500ms</div>
                          </div>
                          <div className="text-center p-4 bg-white rounded-lg border border-blue-200">
                            <div className="text-2xl font-bold text-blue-600 mb-1">✅</div>
                            <div className="font-medium text-gray-900 mb-1">Good</div>
                            <div className="text-sm text-gray-600">500-1000ms</div>
                          </div>
                          <div className="text-center p-4 bg-white rounded-lg border border-yellow-200">
                            <div className="text-2xl font-bold text-yellow-600 mb-1">⚠️</div>
                            <div className="font-medium text-gray-900 mb-1">Slow</div>
                            <div className="text-sm text-gray-600">1000-2000ms</div>
                          </div>
                          <div className="text-center p-4 bg-white rounded-lg border border-red-200">
                            <div className="text-2xl font-bold text-red-600 mb-1">🐌</div>
                            <div className="font-medium text-gray-900 mb-1">Very Slow</div>
                            <div className="text-sm text-gray-600">&gt; 2000ms</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Tier Features</h2>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Tier</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Analytics</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Monitoring</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Alerts</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Free</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Basic usage stats</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Basic monitoring</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Starter</td>
                            <td className="px-6 py-4 text-sm text-green-600">Enhanced analytics</td>
                            <td className="px-6 py-4 text-sm text-green-600">Real-time monitoring</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Basic alerts</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Professional</td>
                            <td className="px-6 py-4 text-sm text-green-600">Advanced analytics</td>
                            <td className="px-6 py-4 text-sm text-green-600">Comprehensive monitoring</td>
                            <td className="px-6 py-4 text-sm text-green-600">Custom alerts</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Enterprise</td>
                            <td className="px-6 py-4 text-sm text-green-600">Full analytics suite</td>
                            <td className="px-6 py-4 text-sm text-green-600">Enterprise monitoring</td>
                            <td className="px-6 py-4 text-sm text-green-600">Advanced alerting</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <Alert type="info">
                    <strong>Performance Optimization:</strong> Use the analytics dashboard to identify bottlenecks, optimize routing strategies, and reduce costs while maintaining response quality.
                  </Alert>
                </div>
              )}

              {activeSection === 'authentication' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Authentication</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Learn how to authenticate with RouKey using API keys.
                    </p>
                  </div>

                  <Alert type="info">
                    <strong>Important:</strong> RouKey uses the <code className="bg-blue-100 px-2 py-1 rounded text-blue-800">X-API-Key</code> header for authentication.
                    Never use <code className="bg-blue-100 px-2 py-1 rounded text-blue-800">Authorization</code> header format.
                  </Alert>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Getting Your API Key</h2>
                    <div className="space-y-4">
                      <p className="text-gray-600 text-lg">
                        To get started with RouKey, you'll need to create an API key from your dashboard:
                      </p>
                      <ol className="list-decimal list-inside space-y-3 text-gray-600 ml-4 text-lg">
                        <li>Sign up for a RouKey account at <a href="https://roukey.online" className="text-[#ff6b35] hover:text-[#e55a2b] underline">roukey.online</a></li>
                        <li>Navigate to your dashboard and create a configuration</li>
                        <li>Add your LLM provider API keys (OpenAI, Anthropic, etc.)</li>
                        <li>Generate a user API key for external access</li>
                        <li>Copy your API key (format: <code className="bg-gray-100 px-2 py-1 rounded text-gray-700">rk_live_...</code>)</li>
                      </ol>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Authentication Methods</h2>
                    <div className="space-y-8">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Method 1: X-API-Key Header (Recommended)</h3>
                        <CodeBlock title="Using X-API-Key header" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Method 2: Bearer Token</h3>
                        <CodeBlock title="Using Authorization Bearer header" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'`}
                        </CodeBlock>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Best Practice:</strong> Always use the <code className="bg-green-100 px-2 py-1 rounded text-green-800">X-API-Key</code> header method
                    as it's the primary authentication method for RouKey and ensures maximum compatibility.
                  </Alert>
                </div>
              )}

              {/* Authentication Subsections */}
              {activeSection === 'authentication-api-keys' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">API Keys</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Learn how to create, manage, and secure your RouKey API keys for optimal performance and security.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Creating API Keys</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey API keys are generated from your dashboard and provide secure access to the intelligent routing system.
                      </p>
                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Key Format</h3>
                        <div className="font-mono text-sm bg-white p-3 rounded border">
                          rk_live_1234567890abcdef...
                        </div>
                        <p className="text-gray-600 text-sm mt-2">
                          All RouKey API keys start with <code className="bg-white px-1 rounded">rk_live_</code> followed by a secure random string.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Key Management</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <KeyIcon className="h-5 w-5 text-green-600" />
                          Best Practices
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Store keys in environment variables</li>
                          <li>• Never commit keys to version control</li>
                          <li>• Rotate keys regularly</li>
                          <li>• Use different keys for different environments</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-red-50 rounded-xl border border-red-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
                          Security Warnings
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Never expose keys in client-side code</li>
                          <li>• Don't share keys in public channels</li>
                          <li>• Revoke compromised keys immediately</li>
                          <li>• Monitor key usage for anomalies</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Tier Limits</h2>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Tier</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Max API Keys</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Features</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Free</td>
                            <td className="px-6 py-4 text-sm text-gray-600">3</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Basic routing, 1 config</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Starter</td>
                            <td className="px-6 py-4 text-sm text-gray-600">50</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Advanced routing, custom roles</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Professional</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Unlimited</td>
                            <td className="px-6 py-4 text-sm text-gray-600">All features, knowledge base</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Enterprise</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Unlimited</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Enterprise features, priority support</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'authentication-security' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Security</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey implements enterprise-grade security measures to protect your API keys and data.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Encryption & Storage</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                          AES-256 Encryption
                        </h3>
                        <p className="text-gray-600 mb-4">
                          All API keys are encrypted using AES-256 encryption before storage in our secure database.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Keys encrypted at rest</li>
                          <li>• Secure key derivation</li>
                          <li>• Regular key rotation</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CloudIcon className="h-5 w-5 text-green-600" />
                          Transit Security
                        </h3>
                        <p className="text-gray-600 mb-4">
                          All communications use TLS 1.3 encryption to protect data in transit.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• TLS 1.3 encryption</li>
                          <li>• Certificate pinning</li>
                          <li>• HSTS headers</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Data Privacy</h2>
                    <div className="space-y-6">
                      <div className="bg-green-50 p-6 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">What We DON'T Store</h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Your actual API responses from providers</li>
                          <li>• Sensitive content from your requests</li>
                          <li>• Personal data beyond what's necessary</li>
                          <li>• Provider API keys in plain text</li>
                        </ul>
                      </div>
                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">What We Store</h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Encrypted provider API keys</li>
                          <li>• Request metadata for routing decisions</li>
                          <li>• Usage analytics (anonymized)</li>
                          <li>• Configuration settings</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Compliance</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                        <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <ShieldCheckIcon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">SOC 2 Ready</h3>
                        <p className="text-gray-600 text-sm">Security controls aligned with SOC 2 Type II requirements</p>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <DocumentTextIcon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">GDPR Compliant</h3>
                        <p className="text-gray-600 text-sm">Full compliance with European data protection regulations</p>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <KeyIcon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">BYOK Model</h3>
                        <p className="text-gray-600 text-sm">You maintain control of your API keys and data</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Authentication Methods Section */}
              {activeSection === 'authentication-auth-methods' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Authentication Methods</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey supports multiple authentication methods to integrate seamlessly with your existing infrastructure and security requirements.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Supported Methods</h2>
                    <div className="space-y-8">
                      <div className="border-l-4 border-[#ff6b35] pl-6">
                        <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <KeyIcon className="h-5 w-5 text-[#ff6b35]" />
                          X-API-Key Header (Recommended)
                        </h3>
                        <p className="text-gray-600 mb-4">
                          The primary and recommended authentication method for RouKey. This method provides the best compatibility and performance.
                        </p>
                        <CodeBlock title="X-API-Key authentication" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}]
  }'`}
                        </CodeBlock>
                        <div className="mt-4 bg-green-50 p-4 rounded-lg border border-green-200">
                          <h4 className="font-medium text-gray-900 mb-2">Benefits:</h4>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Primary authentication method</li>
                            <li>• Maximum compatibility</li>
                            <li>• Optimal performance</li>
                            <li>• Clear separation from OAuth tokens</li>
                          </ul>
                        </div>
                      </div>

                      <div className="border-l-4 border-blue-500 pl-6">
                        <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                          Authorization Bearer Token
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Alternative authentication method using the standard Authorization header with Bearer token format.
                        </p>
                        <CodeBlock title="Bearer token authentication" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}]
  }'`}
                        </CodeBlock>
                        <div className="mt-4 bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <h4 className="font-medium text-gray-900 mb-2">Use Cases:</h4>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Legacy system integration</li>
                            <li>• Standard OAuth workflows</li>
                            <li>• Third-party tool compatibility</li>
                            <li>• Enterprise security requirements</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">SDK Integration</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey is compatible with popular AI SDKs. Here's how to configure authentication for different platforms:
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">OpenAI SDK (Python)</h3>
                          <CodeBlock title="OpenAI Python SDK configuration" language="python">
{`from openai import OpenAI

client = OpenAI(
    api_key="rk_live_your_api_key_here",
    base_url="https://roukey.online/api/external/v1"
)

response = client.chat.completions.create(
    messages=[{"role": "user", "content": "Hello!"}],
    model="gpt-4"  # This will be routed by RouKey
)`}
                          </CodeBlock>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">OpenAI SDK (JavaScript)</h3>
                          <CodeBlock title="OpenAI JavaScript SDK configuration" language="javascript">
{`import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: 'rk_live_your_api_key_here',
  baseURL: 'https://roukey.online/api/external/v1'
});

const response = await openai.chat.completions.create({
  messages: [{ role: 'user', content: 'Hello!' }],
  model: 'gpt-4' // This will be routed by RouKey
});`}
                          </CodeBlock>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Security Best Practices</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CheckIcon className="h-5 w-5 text-green-600" />
                          Recommended Practices
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Use environment variables for API keys</li>
                          <li>• Implement key rotation policies</li>
                          <li>• Monitor API key usage patterns</li>
                          <li>• Use different keys for different environments</li>
                          <li>• Implement proper error handling</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-red-50 rounded-xl border border-red-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
                          Security Warnings
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Never expose keys in client-side code</li>
                          <li>• Don't commit keys to version control</li>
                          <li>• Avoid logging API keys</li>
                          <li>• Don't share keys in public channels</li>
                          <li>• Revoke compromised keys immediately</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Recommendation:</strong> Use the X-API-Key header method for new integrations as it provides the best performance and compatibility with RouKey's routing system.
                  </Alert>
                </div>
              )}

              {/* Rate Limiting Section */}
              {activeSection === 'authentication-rate-limiting' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Rate Limiting</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey provides unlimited API requests across all tiers, focusing on feature-based restrictions rather than rate limiting.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">No Rate Limits Policy</h2>
                    <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
                      <div className="flex items-center gap-3 mb-4">
                        <CheckIcon className="h-6 w-6 text-green-600" />
                        <h3 className="text-xl font-semibold text-gray-900">Unlimited API Requests</h3>
                      </div>
                      <p className="text-gray-600 mb-4">
                        RouKey does not impose rate limits on any subscription tier. You can make as many API requests as needed without worrying about request quotas or throttling.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                          <div className="text-2xl font-bold text-green-600 mb-1">∞</div>
                          <div className="font-medium text-gray-900 mb-1">Free Tier</div>
                          <div className="text-sm text-gray-600">Unlimited requests</div>
                        </div>
                        <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                          <div className="text-2xl font-bold text-green-600 mb-1">∞</div>
                          <div className="font-medium text-gray-900 mb-1">Paid Tiers</div>
                          <div className="text-sm text-gray-600">Unlimited requests</div>
                        </div>
                        <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                          <div className="text-2xl font-bold text-green-600 mb-1">∞</div>
                          <div className="font-medium text-gray-900 mb-1">Enterprise</div>
                          <div className="text-sm text-gray-600">Unlimited requests</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">What We Limit Instead</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Instead of rate limiting, RouKey uses feature-based restrictions to differentiate subscription tiers:
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CogIcon className="h-5 w-5 text-blue-600" />
                            Configuration Limits
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Free: 1 configuration</li>
                            <li>• Starter: 4 configurations</li>
                            <li>• Professional: 20 configurations</li>
                            <li>• Enterprise: Unlimited</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <KeyIcon className="h-5 w-5 text-purple-600" />
                            API Keys Per Config
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Free: 3 keys per config</li>
                            <li>• Starter: 5 keys per config</li>
                            <li>• Professional: 15 keys per config</li>
                            <li>• Enterprise: Unlimited</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <SparklesIcon className="h-5 w-5 text-green-600" />
                            User-Generated API Keys
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Free: 3 total keys</li>
                            <li>• Starter: 50 total keys</li>
                            <li>• Professional: Unlimited</li>
                            <li>• Enterprise: Unlimited</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-yellow-50 rounded-xl border border-yellow-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <UserGroupIcon className="h-5 w-5 text-yellow-600" />
                            Custom Roles & Features
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Free: No custom roles</li>
                            <li>• Starter: 3 custom roles</li>
                            <li>• Professional: Unlimited roles</li>
                            <li>• Enterprise: Unlimited roles</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-orange-50 rounded-xl border border-orange-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <DocumentIcon className="h-5 w-5 text-orange-600" />
                            Knowledge Base Documents
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Free: Not available</li>
                            <li>• Starter: Not available</li>
                            <li>• Professional: 5 documents</li>
                            <li>• Enterprise: 15 documents</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-teal-50 rounded-xl border border-teal-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CircleStackIcon className="h-5 w-5 text-teal-600" />
                            Feature Access
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Advanced routing strategies</li>
                            <li>• Prompt engineering</li>
                            <li>• Semantic caching</li>
                            <li>• Multi-role orchestration</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Provider Rate Limits</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        While RouKey doesn't impose rate limits, your underlying AI providers (OpenAI, Anthropic, etc.) may have their own rate limits:
                      </p>
                      <div className="bg-yellow-50 p-6 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                          Provider Limitations
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• <strong>OpenAI:</strong> Rate limits based on your OpenAI tier</li>
                          <li>• <strong>Anthropic:</strong> Rate limits based on your Claude usage tier</li>
                          <li>• <strong>Google:</strong> Free tier limited to ~60 requests/minute</li>
                          <li>• <strong>Other providers:</strong> Each has their own rate limiting policies</li>
                        </ul>
                      </div>
                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">How RouKey Handles Provider Limits</h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Automatic failover to alternative providers when limits are hit</li>
                          <li>• Intelligent routing to avoid known rate-limited providers</li>
                          <li>• Detection of provider free tiers with lower limits</li>
                          <li>• Load balancing across multiple API keys for the same provider</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Best Practices</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CheckIcon className="h-5 w-5 text-green-600" />
                          Optimization Tips
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Use semantic caching to reduce duplicate requests</li>
                          <li>• Configure multiple API keys per provider for load balancing</li>
                          <li>• Enable streaming for long-running tasks</li>
                          <li>• Use intelligent routing to optimize costs</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                          Monitoring
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Monitor your provider API usage and costs</li>
                          <li>• Set up alerts for unusual usage patterns</li>
                          <li>• Use RouKey's analytics to track performance</li>
                          <li>• Implement proper error handling for provider limits</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <Alert type="info">
                    <strong>No Limits:</strong> RouKey focuses on providing unlimited access to AI models while optimizing costs and performance through intelligent routing rather than restricting usage.
                  </Alert>
                </div>
              )}

              {activeSection === 'api-reference' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">API Reference</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Complete reference for the RouKey API endpoints and parameters.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Base URL</h2>
                    <CodeBlock title="Production Base URL">
{`https://roukey.online/api/external/v1`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Chat Completions</h2>
                    <p className="text-gray-600 mb-6 text-lg">
                      Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.
                    </p>

                    <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl mb-6 border border-green-200">
                      <div className="flex items-center gap-3 mb-3">
                        <span className="bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium">POST</span>
                        <code className="text-gray-900 text-lg font-mono">/chat/completions</code>
                      </div>
                      <p className="text-gray-600">
                        OpenAI-compatible endpoint with RouKey's intelligent routing capabilities
                      </p>
                    </div>

                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Request Parameters</h3>
                    <div className="overflow-x-auto mb-8">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Parameter</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Type</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Required</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Description</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">messages</td>
                            <td className="px-6 py-4 text-sm text-gray-600">array</td>
                            <td className="px-6 py-4 text-sm text-green-600">Yes</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Array of message objects</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">stream</td>
                            <td className="px-6 py-4 text-sm text-gray-600">boolean</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Enable streaming responses (recommended for multi-role tasks)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">temperature</td>
                            <td className="px-6 py-4 text-sm text-gray-600">number</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Sampling temperature (0-2)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">max_tokens</td>
                            <td className="px-6 py-4 text-sm text-gray-600">integer</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Maximum tokens to generate</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">role</td>
                            <td className="px-6 py-4 text-sm text-gray-600">string</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">RouKey-specific role for routing (e.g., "coding", "writing")</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Example Request</h3>
                    <CodeBlock title="Basic chat completion" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Explain quantum computing"}
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 500
}`}
                    </CodeBlock>

                    <h3 className="text-xl font-semibold text-gray-900 mb-4 mt-8">Example with Role-Based Routing</h3>
                    <CodeBlock title="Role-based routing request" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Write a Python function to sort a list"}
  ],
  "role": "coding",
  "stream": true,
  "max_tokens": 1000
}`}
                    </CodeBlock>

                    <Alert type="tip">
                      <strong>Streaming Recommended:</strong> For complex tasks that may involve multiple roles or require significant processing,
                      use <code className="bg-green-100 px-2 py-1 rounded text-green-800">stream: true</code> to avoid timeouts and get real-time responses.
                    </Alert>
                  </div>
                </div>
              )}

              {/* Use Cases Subsections */}
              {activeSection === 'use-cases-development-coding' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Development & Coding</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Optimize your development workflow with RouKey's intelligent routing for coding tasks.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Recommended Setup</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Primary: Claude 4 Opus</h3>
                        <p className="text-gray-600 mb-3">Best coding model with excellent performance for programming tasks</p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Superior code generation and completion</li>
                          <li>• Multi-language support (Python, JavaScript, Go, Rust, etc.)</li>
                          <li>• Cost-effective for high-volume coding tasks</li>
                          <li>• Excellent at debugging and code explanation</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Fallback: GPT-4 / Deepseek R1 0528</h3>
                        <p className="text-gray-600 mb-3">Premium models for complex architectural decisions and code reviews</p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Advanced reasoning for complex problems</li>
                          <li>• Excellent code review and optimization suggestions</li>
                          <li>• Strong performance on system design questions</li>
                          <li>• Better at explaining complex concepts</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Common Use Cases</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="p-4 bg-gray-50 rounded-xl">
                          <h3 className="font-semibold text-gray-900 mb-2">Code Generation</h3>
                          <p className="text-gray-600 text-sm">Generate functions, classes, and complete modules from natural language descriptions.</p>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-xl">
                          <h3 className="font-semibold text-gray-900 mb-2">Code Review</h3>
                          <p className="text-gray-600 text-sm">Automated code review with suggestions for improvements and bug detection.</p>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-xl">
                          <h3 className="font-semibold text-gray-900 mb-2">Documentation</h3>
                          <p className="text-gray-600 text-sm">Generate comprehensive documentation, comments, and API references.</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="p-4 bg-gray-50 rounded-xl">
                          <h3 className="font-semibold text-gray-900 mb-2">Debugging</h3>
                          <p className="text-gray-600 text-sm">Identify bugs, suggest fixes, and explain error messages in detail.</p>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-xl">
                          <h3 className="font-semibold text-gray-900 mb-2">Code Translation</h3>
                          <p className="text-gray-600 text-sm">Convert code between different programming languages and frameworks.</p>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-xl">
                          <h3 className="font-semibold text-gray-900 mb-2">Learning & Tutorials</h3>
                          <p className="text-gray-600 text-sm">Interactive coding tutorials and explanations for learning new technologies.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Setup Guide</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Step 1: Enable Intelligent Role Routing</h3>
                        <p className="text-gray-600 mb-3">In your RouKey app, navigate to your Custom Model configuration and set the routing strategy to "Intelligent Role Routing".</p>
                        <div className="bg-white p-3 rounded border text-sm text-gray-700 font-mono">
                          Routing Strategy: Intelligent Role Routing
                        </div>
                      </div>

                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Step 2: Add Your API Keys</h3>
                        <p className="text-gray-600 mb-3">Add API keys for different providers optimized for coding tasks:</p>
                        <ul className="text-gray-600 text-sm space-y-1 mb-3">
                          <li>• <strong>Deepseek Coder:</strong> Primary coding model</li>
                          <li>• <strong>Claude 3.5 Sonnet:</strong> Code review and analysis</li>
                          <li>• <strong>GPT-4:</strong> Documentation and explanations</li>
                        </ul>
                      </div>

                      <div className="p-6 bg-orange-50 rounded-xl border border-orange-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Step 3: Assign Roles to API Keys</h3>
                        <p className="text-gray-600 mb-3">In the "My Models" section, assign specific roles to each API key:</p>
                        <div className="space-y-2 text-sm">
                          <div className="bg-white p-2 rounded border">
                            <span className="font-medium">Deepseek Coder API Key</span> → <span className="text-blue-600">coding</span> role
                          </div>
                          <div className="bg-white p-2 rounded border">
                            <span className="font-medium">Claude 3.5 Sonnet API Key</span> → <span className="text-green-600">code_review</span> role
                          </div>
                          <div className="bg-white p-2 rounded border">
                            <span className="font-medium">GPT-4 API Key</span> → <span className="text-purple-600">documentation</span> role
                          </div>
                        </div>
                      </div>

                      <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                        <h3 className="font-semibold text-gray-900 mb-3">How It Works</h3>
                        <p className="text-gray-600 text-sm">
                          When you send a request, RouKey's AI automatically classifies your prompt and routes it to the appropriate API key based on the assigned roles. No complex configuration needed - just assign roles in the app interface.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'use-cases-content-creation' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Content Creation</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Enhance your content creation workflow with RouKey's intelligent routing for writing tasks.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Recommended Setup</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Primary: Claude 4 Opus</h3>
                        <p className="text-gray-600 mb-3">Exceptional writing quality with natural, engaging tone</p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Superior creative writing capabilities</li>
                          <li>• Excellent at maintaining consistent tone and style</li>
                          <li>• Strong performance on long-form content</li>
                          <li>• Great for editing and improving existing content</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Fallback: GPT-4 / Gemini 2.5 Pro</h3>
                        <p className="text-gray-600 mb-3">Versatile models for diverse content types and quick generation</p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Fast content generation for high-volume needs</li>
                          <li>• Good at research and fact-checking</li>
                          <li>• Excellent for SEO-optimized content</li>
                          <li>• Cost-effective for bulk content creation</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Content Types</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Blog Posts & Articles</h3>
                        <p className="text-gray-600 text-sm mb-3">Long-form content with research and SEO optimization</p>
                        <div className="text-xs text-blue-600 font-medium">Temperature: 0.7</div>
                      </div>
                      <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Marketing Copy</h3>
                        <p className="text-gray-600 text-sm mb-3">Persuasive copy for ads, emails, and landing pages</p>
                        <div className="text-xs text-green-600 font-medium">Temperature: 0.8</div>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Creative Writing</h3>
                        <p className="text-gray-600 text-sm mb-3">Stories, scripts, and imaginative content</p>
                        <div className="text-xs text-purple-600 font-medium">Temperature: 0.9</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'use-cases' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">
                      Use Cases
                    </h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Discover how RouKey can optimize your specific use case with intelligent routing and cost savings.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Development & Coding</h3>
                      <p className="text-gray-600 mb-4">Optimize your development workflow with specialized coding models and intelligent routing.</p>
                      <div className="text-[#ff6b35] font-medium">→ Development Guide</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Content Creation</h3>
                      <p className="text-gray-600 mb-4">Enhance your content creation with models optimized for writing and creativity.</p>
                      <div className="text-[#ff6b35] font-medium">→ Content Creation Guide</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Enterprise Applications</h3>
                      <p className="text-gray-600 mb-4">Scale your enterprise AI applications with reliable routing and cost optimization.</p>
                      <div className="text-[#ff6b35] font-medium">→ Enterprise Guide</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Educational Platforms</h3>
                      <p className="text-gray-600 mb-4">Build intelligent tutoring systems and educational tools with RouKey.</p>
                      <div className="text-[#ff6b35] font-medium">→ Education Guide</div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Development & Coding</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Code Generation & Review</h3>
                        <p className="text-gray-600 mb-4">
                          Route coding tasks to specialized models like Claude 4 Opus for optimal code quality and cost efficiency.
                        </p>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2">Recommended Strategy:</h4>
                          <p className="text-gray-600 text-sm">Intelligent Role Routing with "coding" role detection</p>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Documentation & Comments</h3>
                        <p className="text-gray-600 mb-4">
                          Automatically route documentation tasks to cost-effective models while maintaining quality.
                        </p>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2">Cost Savings:</h4>
                          <p className="text-gray-600 text-sm">Up to 70% reduction using complexity-based routing</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Content Creation</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Blog Posts & Articles</h3>
                        <p className="text-gray-600 mb-4">
                          Route creative writing tasks to models optimized for content generation with appropriate complexity analysis.
                        </p>
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <h4 className="font-medium text-gray-900 mb-2">Multi-Role Workflow:</h4>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Research and outline generation</li>
                            <li>• Content writing and expansion</li>
                            <li>• Editing and refinement</li>
                            <li>• SEO optimization</li>
                          </ul>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Marketing Copy</h3>
                        <p className="text-gray-600 mb-4">
                          Optimize marketing content creation with role-based routing for different content types.
                        </p>
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <h4 className="font-medium text-gray-900 mb-2">Specialized Roles:</h4>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Email campaigns</li>
                            <li>• Social media posts</li>
                            <li>• Ad copy generation</li>
                            <li>• Product descriptions</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Enterprise Applications</h2>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Customer Support</h3>
                          <p className="text-gray-600 mb-4">Intelligent routing based on query complexity and urgency.</p>
                          <div className="text-sm text-purple-600 font-medium">Strategy: Complexity + Fallback</div>
                        </div>
                        <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Data Analysis</h3>
                          <p className="text-gray-600 mb-4">Route analytical tasks to models optimized for reasoning and computation.</p>
                          <div className="text-sm text-blue-600 font-medium">Strategy: Role-based Routing</div>
                        </div>
                        <div className="p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Document Processing</h3>
                          <p className="text-gray-600 mb-4">Efficient processing of large document sets with cost optimization.</p>
                          <div className="text-sm text-green-600 font-medium">Strategy: Cost-optimized</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Educational Platforms</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Personalized Learning</h3>
                        <p className="text-gray-600 mb-4">
                          Adapt content difficulty and teaching style based on student needs with intelligent routing.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Beginner explanations → Cost-effective models</li>
                          <li>• Advanced topics → Premium models</li>
                          <li>• Interactive exercises → Specialized models</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Content Generation</h3>
                        <p className="text-gray-600 mb-4">
                          Generate educational content at scale while maintaining quality and controlling costs.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Quiz generation</li>
                          <li>• Lesson plan creation</li>
                          <li>• Assignment feedback</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Research & Analysis</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey's multi-role orchestration excels at complex research workflows that require coordination between different AI capabilities.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Market Research</h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Data collection and web browsing</li>
                            <li>• Competitive analysis</li>
                            <li>• Trend identification</li>
                            <li>• Report generation</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Academic Research</h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Literature review</li>
                            <li>• Data analysis</li>
                            <li>• Hypothesis generation</li>
                            <li>• Paper writing assistance</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white">
                    <h3 className="text-xl font-semibold mb-4">Ready to optimize your use case?</h3>
                    <p className="text-white/90 mb-6">
                      Contact our team to discuss how RouKey can be tailored to your specific requirements and use case.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <button
                        onClick={() => setActiveSection('getting-started')}
                        className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                      >
                        Get Started
                      </button>
                      <a
                        href="mailto:<EMAIL>"
                        className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors text-center"
                      >
                        Contact Sales
                      </a>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'use-cases-enterprise-apps' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Enterprise Applications</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Scale your enterprise AI applications with RouKey's reliable routing, cost optimization, and enterprise-grade features.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Enterprise Benefits</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Cost Optimization</h3>
                        <p className="text-gray-600 text-sm mb-3">Reduce AI costs by 40-60% through intelligent routing</p>
                        <ul className="text-gray-600 text-xs space-y-1">
                          <li>• Route simple queries to cheaper models</li>
                          <li>• Use premium models only when necessary</li>
                          <li>• Automatic cost tracking and analytics</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Reliability & Scale</h3>
                        <p className="text-gray-600 text-sm mb-3">99.9% uptime with automatic failover</p>
                        <ul className="text-gray-600 text-xs space-y-1">
                          <li>• Multiple provider redundancy</li>
                          <li>• Automatic retry logic</li>
                          <li>• Load balancing across API keys</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Security & Compliance</h3>
                        <p className="text-gray-600 text-sm mb-3">Enterprise-grade security and data protection</p>
                        <ul className="text-gray-600 text-xs space-y-1">
                          <li>• Encrypted API key storage</li>
                          <li>• SOC 2 compliance ready</li>
                          <li>• Audit logs and monitoring</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Analytics & Insights</h3>
                        <p className="text-gray-600 text-sm mb-3">Comprehensive usage analytics and reporting</p>
                        <ul className="text-gray-600 text-xs space-y-1">
                          <li>• Cost breakdown by model/provider</li>
                          <li>• Performance metrics and trends</li>
                          <li>• Custom reporting dashboards</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Common Enterprise Use Cases</h2>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-50 rounded-xl">
                        <h3 className="font-semibold text-gray-900 mb-2">Customer Support Automation</h3>
                        <p className="text-gray-600 text-sm">Intelligent chatbots and support ticket routing with cost-optimized model selection.</p>
                      </div>
                      <div className="p-4 bg-gray-50 rounded-xl">
                        <h3 className="font-semibold text-gray-900 mb-2">Document Processing</h3>
                        <p className="text-gray-600 text-sm">Large-scale document analysis, summarization, and data extraction workflows.</p>
                      </div>
                      <div className="p-4 bg-gray-50 rounded-xl">
                        <h3 className="font-semibold text-gray-900 mb-2">Internal Tools & Automation</h3>
                        <p className="text-gray-600 text-sm">AI-powered internal tools for HR, legal, finance, and operations teams.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'use-cases-educational-platforms' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Educational Platforms</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Build intelligent tutoring systems and educational tools with RouKey's adaptive routing and cost-effective scaling.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Educational Applications</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Intelligent Tutoring</h3>
                          <p className="text-gray-600 text-sm">Personalized learning experiences with adaptive difficulty and explanations.</p>
                        </div>
                        <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Homework Assistance</h3>
                          <p className="text-gray-600 text-sm">Step-by-step problem solving and concept explanations across subjects.</p>
                        </div>
                        <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Content Generation</h3>
                          <p className="text-gray-600 text-sm">Automated quiz generation, lesson plans, and educational materials.</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Language Learning</h3>
                          <p className="text-gray-600 text-sm">Conversational practice, grammar correction, and pronunciation feedback.</p>
                        </div>
                        <div className="p-4 bg-red-50 rounded-xl border border-red-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Assessment & Grading</h3>
                          <p className="text-gray-600 text-sm">Automated essay grading, feedback generation, and learning analytics.</p>
                        </div>
                        <div className="p-4 bg-indigo-50 rounded-xl border border-indigo-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Research Assistance</h3>
                          <p className="text-gray-600 text-sm">Academic research support, citation help, and literature reviews.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Cost-Effective Scaling</h2>
                    <p className="text-gray-600 mb-6">
                      Educational platforms often need to serve thousands of students cost-effectively. RouKey's intelligent routing helps optimize costs:
                    </p>
                    <div className="space-y-4">
                      <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Simple Questions → Cheaper Models</h3>
                        <p className="text-gray-600 text-sm">Basic math problems, vocabulary questions, and factual queries use cost-effective models.</p>
                      </div>
                      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Complex Problems → Premium Models</h3>
                        <p className="text-gray-600 text-sm">Advanced problem-solving, essay feedback, and detailed explanations use higher-quality models.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'use-cases-research-analysis' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Research & Analysis</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Accelerate your research and analysis workflows with RouKey's intelligent routing for data processing and insights generation.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Research Applications</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Literature Review</h3>
                          <p className="text-gray-600 text-sm">Automated paper summarization, key finding extraction, and research gap identification.</p>
                        </div>
                        <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Data Analysis</h3>
                          <p className="text-gray-600 text-sm">Statistical analysis interpretation, trend identification, and insight generation.</p>
                        </div>
                        <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Report Generation</h3>
                          <p className="text-gray-600 text-sm">Automated research reports, executive summaries, and presentation materials.</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Survey Analysis</h3>
                          <p className="text-gray-600 text-sm">Qualitative data analysis, sentiment analysis, and theme extraction.</p>
                        </div>
                        <div className="p-4 bg-red-50 rounded-xl border border-red-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Hypothesis Generation</h3>
                          <p className="text-gray-600 text-sm">Research question formulation and experimental design suggestions.</p>
                        </div>
                        <div className="p-4 bg-indigo-50 rounded-xl border border-indigo-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Citation & References</h3>
                          <p className="text-gray-600 text-sm">Automated citation formatting, reference checking, and bibliography generation.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Recommended Model Strategy</h2>
                    <div className="space-y-4">
                      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Analysis & Reasoning: GPT-4 / Claude 4 Opus</h3>
                        <p className="text-gray-600 text-sm">Complex analytical tasks requiring deep reasoning and nuanced understanding.</p>
                      </div>
                      <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Data Processing: Gemini 2.5 Pro</h3>
                        <p className="text-gray-600 text-sm">Large-scale data processing, summarization, and pattern recognition tasks.</p>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Writing & Reports: Claude 4 Opus</h3>
                        <p className="text-gray-600 text-sm">High-quality research writing, report generation, and academic formatting.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Examples Subsections */}
              {activeSection === 'examples-javascript-examples' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">JavaScript/Node.js Examples</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Complete JavaScript and Node.js examples for integrating RouKey into your applications.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Basic Fetch Example</h2>
                    <CodeBlock title="Simple chat completion with fetch" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Hello! How does RouKey work?' }
    ],
    stream: false
    // Note: model parameter is optional - RouKey routes based on your configuration
  })
});

const data = await response.json();
console.log(data.choices[0].message.content);`}
                    </CodeBlock>
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <p className="text-sm text-gray-600">
                        <strong>Note:</strong> RouKey automatically routes your request based on your configured routing strategy and API keys.
                        The model parameter is optional and used for OpenAI compatibility.
                      </p>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Streaming Example</h2>
                    <CodeBlock title="Streaming responses with Server-Sent Events" language="javascript">
{`async function streamCompletion() {
  const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': 'rk_live_your_api_key_here'
    },
    body: JSON.stringify({
      messages: [
        { role: 'user', content: 'Write a short story about AI' }
      ],
      stream: true
    })
  });

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    const chunk = decoder.decode(value);
    const lines = chunk.split('\\n');

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = line.slice(6);
        if (data === '[DONE]') return;

        try {
          const parsed = JSON.parse(data);
          const content = parsed.choices[0]?.delta?.content;
          if (content) {
            process.stdout.write(content);
          }
        } catch (e) {
          // Skip invalid JSON
        }
      }
    }
  }
}`}
                    </CodeBlock>
                    <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                      <p className="text-sm text-gray-600">
                        <strong>Streaming:</strong> RouKey automatically handles streaming for complex multi-role tasks.
                        Your routing strategy determines which models are used for the response.
                      </p>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Express.js Integration</h2>
                    <CodeBlock title="Express.js API endpoint with RouKey" language="javascript">
{`const express = require('express');
const app = express();

app.use(express.json());

app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;

    const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': process.env.ROUKEY_API_KEY
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: message }
        ],
        stream: false,
        temperature: 0.7  // Optional: customize parameters
      })
    });

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json({ error: data.error });
    }

    res.json({
      response: data.choices[0].message.content,
      usage: data.usage,
      rokey_metadata: data.rokey_metadata  // RouKey-specific information
    });
  } catch (error) {
    console.error('RouKey API Error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});`}
                    </CodeBlock>
                  </div>
                </div>
              )}

              {activeSection === 'examples-python-examples' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Python Examples</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Complete Python examples for integrating RouKey into your applications and scripts.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Basic Requests Example</h2>
                    <CodeBlock title="Simple chat completion with requests" language="python">
{`import requests
import json

def chat_with_roukey(message):
    url = "https://roukey.online/api/external/v1/chat/completions"

    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "rk_live_your_api_key_here"
    }

    data = {
        "messages": [
            {"role": "user", "content": message}
        ],
        "stream": False,
        "temperature": 0.7  # Optional: customize parameters
        # Note: model parameter is optional - RouKey routes automatically
    }

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        result = response.json()
        print(f"Routing info: {result.get('rokey_metadata', {})}")
        return result["choices"][0]["message"]["content"]
    else:
        print(f"Error: {response.status_code} - {response.text}")
        return None

# Usage
response = chat_with_roukey("Hello! How does RouKey work?")
print(response)`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Streaming Example</h2>
                    <CodeBlock title="Streaming responses with requests" language="python">
{`import requests
import json

def stream_chat_completion(message):
    url = "https://roukey.online/api/external/v1/chat/completions"

    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "rk_live_your_api_key_here"
    }

    data = {
        "messages": [
            {"role": "user", "content": message}
        ],
        "stream": True
    }

    response = requests.post(url, headers=headers, json=data, stream=True)

    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith('data: '):
                data_str = line[6:]  # Remove 'data: ' prefix
                if data_str == '[DONE]':
                    break

                try:
                    data = json.loads(data_str)
                    content = data.get('choices', [{}])[0].get('delta', {}).get('content', '')
                    if content:
                        print(content, end='', flush=True)
                except json.JSONDecodeError:
                    continue

# Usage
stream_chat_completion("Write a short story about AI")
print()  # New line after streaming`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">FastAPI Integration</h2>
                    <CodeBlock title="FastAPI endpoint with RouKey" language="python">
{`from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import requests
import os

app = FastAPI()

class ChatRequest(BaseModel):
    message: str
    temperature: float = 0.7  # Optional parameter

class ChatResponse(BaseModel):
    response: str
    usage: dict
    routing_info: dict  # RouKey metadata

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    try:
        url = "https://roukey.online/api/external/v1/chat/completions"

        headers = {
            "Content-Type": "application/json",
            "X-API-Key": os.getenv("ROUKEY_API_KEY")
        }

        data = {
            "messages": [
                {"role": "user", "content": request.message}
            ],
            "stream": False,
            "temperature": request.temperature
        }

        response = requests.post(url, headers=headers, json=data)

        if response.status_code != 200:
            raise HTTPException(
                status_code=response.status_code,
                detail=f"RouKey API error: {response.text}"
            )

        result = response.json()

        return ChatResponse(
            response=result["choices"][0]["message"]["content"],
            usage=result.get("usage", {}),
            routing_info=result.get("rokey_metadata", {})
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)`}
                    </CodeBlock>
                  </div>
                </div>
              )}

              {activeSection === 'examples' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Examples</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Practical examples to get you started with RouKey in different programming languages.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">JavaScript/Node.js</h3>
                      <p className="text-gray-600 mb-4">Complete examples for web applications and Node.js backends.</p>
                      <div className="text-[#ff6b35] font-medium">→ JavaScript Examples</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Python</h3>
                      <p className="text-gray-600 mb-4">Python examples for data science, web apps, and automation.</p>
                      <div className="text-[#ff6b35] font-medium">→ Python Examples</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">cURL</h3>
                      <p className="text-gray-600 mb-4">Command-line examples for testing and shell scripting.</p>
                      <div className="text-[#ff6b35] font-medium">→ cURL Examples</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">OpenAI SDK Integration</h3>
                      <p className="text-gray-600 mb-4">Drop-in replacement examples using OpenAI SDKs.</p>
                      <div className="text-[#ff6b35] font-medium">→ OpenAI SDK Examples</div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">JavaScript/Node.js</h2>
                    <CodeBlock title="Basic chat completion with fetch" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Explain machine learning in simple terms' }
    ],
    stream: false,
    max_tokens: 500
  })
});

const data = await response.json();
console.log(data.choices[0].message.content);`}
                    </CodeBlock>

                    <CodeBlock title="Streaming response example" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Write a detailed explanation of quantum computing' }
    ],
    stream: true,
    max_tokens: 1000
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  const chunk = decoder.decode(value);
  const lines = chunk.split('\\n');

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') return;

      try {
        const parsed = JSON.parse(data);
        const content = parsed.choices[0]?.delta?.content;
        if (content) {
          process.stdout.write(content);
        }
      } catch (e) {
        // Skip invalid JSON
      }
    }
  }
}`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Python</h2>
                    <CodeBlock title="Basic chat completion with requests" language="python">
{`import requests
import json

response = requests.post(
    'https://roukey.online/api/external/v1/chat/completions',
    headers={
        'Content-Type': 'application/json',
        'X-API-Key': 'rk_live_your_api_key_here'
    },
    json={
        'messages': [
            {'role': 'user', 'content': 'Explain machine learning in simple terms'}
        ],
        'stream': False,
        'max_tokens': 500
    }
)

data = response.json()
print(data['choices'][0]['message']['content'])`}
                    </CodeBlock>

                    <CodeBlock title="Streaming response with requests" language="python">
{`import requests
import json

response = requests.post(
    'https://roukey.online/api/external/v1/chat/completions',
    headers={
        'Content-Type': 'application/json',
        'X-API-Key': 'rk_live_your_api_key_here'
    },
    json={
        'messages': [
            {'role': 'user', 'content': 'Write a detailed explanation of quantum computing'}
        ],
        'stream': True,
        'max_tokens': 1000
    },
    stream=True
)

for line in response.iter_lines():
    if line:
        line = line.decode('utf-8')
        if line.startswith('data: '):
            data = line[6:]
            if data == '[DONE]':
                break
            try:
                parsed = json.loads(data)
                content = parsed['choices'][0]['delta'].get('content', '')
                if content:
                    print(content, end='', flush=True)
            except json.JSONDecodeError:
                continue`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">cURL</h2>
                    <CodeBlock title="Basic request with cURL" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "stream": false,
    "max_tokens": 150
  }'`}
                    </CodeBlock>

                    <CodeBlock title="Role-based routing with cURL" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}
    ],
    "role": "coding",
    "stream": true,
    "max_tokens": 500
  }'`}
                    </CodeBlock>
                  </div>

                  <Alert type="info">
                    <strong>Need more examples?</strong> Check out our GitHub repository for complete example applications
                    and integration guides for popular frameworks like React, Vue, and Express.js.
                  </Alert>
                </div>
              )}

              {activeSection === 'examples-curl-examples' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">cURL Examples</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Command-line examples using cURL for testing RouKey API endpoints and shell scripting.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Basic Chat Completion</h2>
                    <CodeBlock title="Simple chat completion request" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Hello! How does RouKey work?"}
    ],
    "stream": false
  }'`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Streaming Request</h2>
                    <CodeBlock title="Streaming chat completion" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Write a short story about AI"}
    ],
    "stream": true
  }' \\
  --no-buffer`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Advanced Parameters</h2>
                    <CodeBlock title="Request with custom parameters" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "system", "content": "You are a helpful coding assistant."},
      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}
    ],
    "max_tokens": 500,
    "temperature": 0.2,
    "stream": false
  }'`}
                    </CodeBlock>
                    <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                      <p className="text-sm text-gray-600">
                        <strong>Parameters:</strong> RouKey supports all standard OpenAI parameters.
                        The actual model used depends on your RouKey configuration, not a model parameter.
                      </p>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Shell Script Example</h2>
                    <CodeBlock title="Bash script for batch processing" language="bash">
{`#!/bin/bash

# RouKey API configuration
API_KEY="rk_live_your_api_key_here"
BASE_URL="https://roukey.online/api/external/v1"

# Function to call RouKey API
call_roukey() {
    local message="$1"

    curl -s -X POST "$BASE_URL/chat/completions" \\
        -H "Content-Type: application/json" \\
        -H "X-API-Key: $API_KEY" \\
        -d "{
            \\"messages\\": [
                {\\"role\\": \\"user\\", \\"content\\": \\"$message\\"}
            ],
            \\"stream\\": false
        }" | jq -r '.choices[0].message.content'
}

# Process multiple queries
queries=(
    "What is machine learning?"
    "Explain neural networks"
    "How does deep learning work?"
)

for query in "\${queries[@]}"; do
    echo "Query: $query"
    echo "Response: $(call_roukey "$query")"
    echo "---"
done`}
                    </CodeBlock>
                  </div>
                </div>
              )}

              {activeSection === 'examples-openai-sdk' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">OpenAI SDK Integration</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Drop-in replacement examples using official OpenAI SDKs with RouKey as the backend.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Python OpenAI SDK</h2>
                    <CodeBlock title="OpenAI Python SDK with RouKey" language="python">
{`from openai import OpenAI

# Initialize OpenAI client with RouKey
client = OpenAI(
    api_key="rk_live_your_api_key_here",
    base_url="https://roukey.online/api/external/v1"
)

# Standard chat completion
response = client.chat.completions.create(
    messages=[
        {"role": "user", "content": "Hello! How does RouKey work?"}
    ],
    # model parameter is optional - RouKey routes based on your configuration
    temperature=0.7,  # Optional: customize parameters
    stream=False
)

print(response.choices[0].message.content)
print(f"Routing info: {getattr(response, 'rokey_metadata', {})}")

# Streaming example
stream = client.chat.completions.create(
    messages=[
        {"role": "user", "content": "Write a short story about AI"}
    ],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")`}
                    </CodeBlock>
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <p className="text-sm text-gray-600">
                        <strong>SDK Integration:</strong> RouKey works as a drop-in replacement for OpenAI's API.
                        The model parameter is optional - RouKey automatically routes based on your dashboard configuration.
                      </p>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">JavaScript OpenAI SDK</h2>
                    <CodeBlock title="OpenAI JavaScript SDK with RouKey" language="javascript">
{`import OpenAI from 'openai';

// Initialize OpenAI client with RouKey
const openai = new OpenAI({
  apiKey: 'rk_live_your_api_key_here',
  baseURL: 'https://roukey.online/api/external/v1'
});

// Standard chat completion
async function chatCompletion() {
  const response = await openai.chat.completions.create({
    messages: [
      { role: 'user', content: 'Hello! How does RouKey work?' }
    ],
    // model parameter is optional - RouKey routes automatically
    temperature: 0.7,  // Optional: customize parameters
    stream: false
  });

  console.log(response.choices[0].message.content);
  console.log('Routing info:', response.rokey_metadata);
}

// Streaming example
async function streamingCompletion() {
  const stream = await openai.chat.completions.create({
    messages: [
      { role: 'user', content: 'Write a short story about AI' }
    ],
    stream: true
  });

  for await (const chunk of stream) {
    const content = chunk.choices[0]?.delta?.content;
    if (content) {
      process.stdout.write(content);
    }
  }
}

// Usage
chatCompletion();
streamingCompletion();`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Migration Guide</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Easy Migration</h3>
                        <p className="text-gray-600 mb-3">
                          Migrating from OpenAI to RouKey requires only two changes to your existing code:
                        </p>
                        <ul className="text-gray-600 space-y-2">
                          <li>• Change the <code className="bg-gray-100 px-2 py-1 rounded">api_key</code> to your RouKey API key</li>
                          <li>• Set <code className="bg-gray-100 px-2 py-1 rounded">base_url</code> to RouKey's endpoint</li>
                        </ul>
                      </div>

                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Benefits of Using RouKey</h3>
                        <ul className="text-gray-600 space-y-2">
                          <li>• <strong>Cost Optimization:</strong> Intelligent routing reduces costs by 40-60%</li>
                          <li>• <strong>Higher Reliability:</strong> Automatic failover across multiple providers</li>
                          <li>• <strong>More Models:</strong> Access to 300+ models from different providers</li>
                          <li>• <strong>Same Interface:</strong> No code changes needed beyond configuration</li>
                        </ul>
                      </div>

                      <div className="p-6 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-3">⚠️ Important: Model Parameter Behavior</h3>
                        <p className="text-gray-600 mb-3">
                          Unlike OpenAI's API, RouKey doesn't use the <code className="bg-gray-100 px-2 py-1 rounded">model</code> parameter for routing:
                        </p>
                        <ul className="text-gray-600 space-y-2">
                          <li>• The <code className="bg-gray-100 px-2 py-1 rounded">model</code> parameter is optional and ignored for routing</li>
                          <li>• RouKey routes based on your <strong>dashboard configuration</strong></li>
                          <li>• Your routing strategy determines which models are actually used</li>
                          <li>• This allows for intelligent cost optimization and failover</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* API Reference Subsections */}
              {activeSection === 'api-reference-base-url' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Base URL</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey's API is accessible through a single base URL for all endpoints.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Production Base URL</h2>
                    <CodeBlock title="Production Base URL">
{`https://roukey.online/api/external/v1`}
                    </CodeBlock>
                    <p className="text-gray-600 mt-4">
                      All API requests should be made to endpoints under this base URL. For example, the chat completions endpoint would be:
                    </p>
                    <CodeBlock title="Chat completions endpoint">
{`https://roukey.online/api/external/v1/chat/completions`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">HTTPS Required</h2>
                    <Alert type="warning">
                      <strong>Security Notice:</strong> All API requests must use HTTPS. HTTP requests will be rejected for security reasons.
                    </Alert>
                  </div>
                </div>
              )}

              {activeSection === 'api-reference-chat-completions' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Chat Completions</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.
                    </p>
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl mb-6 border border-green-200">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium">POST</span>
                      <code className="text-gray-900 text-lg font-mono">/chat/completions</code>
                    </div>
                    <p className="text-gray-600">
                      OpenAI-compatible endpoint with RouKey's intelligent routing capabilities
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Basic Example</h2>
                    <CodeBlock title="Basic chat completion request" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Hello! How does RouKey work?"}
    ],
    "stream": false
  }'`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Multimodal Content Support</h2>
                    <p className="text-gray-600 mb-4">
                      RouKey supports multimodal content including images, allowing you to send both text and visual content in your messages.
                    </p>
                    <CodeBlock title="Multimodal request with image" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What do you see in this image?"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image.jpg"
            }
          }
        ]
      }
    ],
    "stream": false
  }'`}
                    </CodeBlock>
                    <Alert type="info">
                      <strong>Multimodal Support:</strong> RouKey supports sending images to any configured model. Ensure your selected models have vision capabilities for optimal image processing.
                    </Alert>
                  </div>

                  <div className="card p-8 bg-blue-50 border border-blue-200">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">🎯 How RouKey Routing Works</h2>
                    <div className="space-y-4 text-gray-700">
                      <p className="text-lg">
                        <strong>Important:</strong> RouKey doesn't use the <code className="bg-white px-2 py-1 rounded text-sm">model</code> parameter for routing decisions.
                      </p>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">✅ What Controls Routing:</h3>
                          <ul className="space-y-1 text-sm">
                            <li>• Your configured routing strategy</li>
                            <li>• API keys you've added to your setup</li>
                            <li>• Intelligent role detection</li>
                            <li>• Complexity analysis</li>
                          </ul>
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">ℹ️ Model Parameter:</h3>
                          <ul className="space-y-1 text-sm">
                            <li>• Optional (for OpenAI compatibility)</li>
                            <li>• Defaults to 'gpt-4.1' (compatibility only)</li>
                            <li>• Doesn't affect which model is used</li>
                            <li>• Actual model depends on your configuration</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Request Parameters</h2>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Parameter</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Type</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Required</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Description</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">messages</td>
                            <td className="px-6 py-4 text-sm text-gray-600">array</td>
                            <td className="px-6 py-4 text-sm text-green-600">Yes</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Array of message objects with role and content (supports multimodal content)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">stream</td>
                            <td className="px-6 py-4 text-sm text-gray-600">boolean</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Whether to stream the response (default: false)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">temperature</td>
                            <td className="px-6 py-4 text-sm text-gray-600">number</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Sampling temperature (0.0 to 2.0)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">max_tokens</td>
                            <td className="px-6 py-4 text-sm text-gray-600">integer</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Maximum tokens to generate</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">top_p</td>
                            <td className="px-6 py-4 text-sm text-gray-600">number</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Nucleus sampling parameter (0.0 to 1.0)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">frequency_penalty</td>
                            <td className="px-6 py-4 text-sm text-gray-600">number</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Frequency penalty (-2.0 to 2.0)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">presence_penalty</td>
                            <td className="px-6 py-4 text-sm text-gray-600">number</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Presence penalty (-2.0 to 2.0)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">stop</td>
                            <td className="px-6 py-4 text-sm text-gray-600">string | array</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Stop sequences (string or array of strings)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">n</td>
                            <td className="px-6 py-4 text-sm text-gray-600">integer</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Number of completions to generate (default: 1)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">role</td>
                            <td className="px-6 py-4 text-sm text-gray-600">string</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">
                              <span className="text-[#ff6b35] font-medium">RouKey-specific</span> -
                              Custom role for intelligent routing
                            </td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">model</td>
                            <td className="px-6 py-4 text-sm text-gray-600">string</td>
                            <td className="px-6 py-4 text-sm text-gray-600">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">
                              <span className="text-yellow-600 font-medium">OpenAI compatibility only</span> -
                              Does not control routing; RouKey routes based on your configuration.
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'api-reference-streaming' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Streaming</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey supports streaming responses for real-time applications and better user experience.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Streaming Request</h2>
                    <CodeBlock title="Streaming chat completion" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Write a short story about AI"}
    ],
    "stream": true
  }'`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Streaming Response Format</h2>
                    <p className="text-gray-600 mb-4">
                      Streaming responses are sent as Server-Sent Events (SSE) with each chunk containing a JSON object:
                    </p>
                    <CodeBlock title="Streaming response chunk" language="json">
{`data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"content":"Hello"},"index":0,"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"content":" there!"},"index":0,"finish_reason":null}]}

data: [DONE]`}
                    </CodeBlock>
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <p className="text-sm text-gray-600">
                        <strong>Note:</strong> The model field in the response reflects the actual model used by RouKey's routing system,
                        not necessarily the model parameter from your request.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'api-reference-async-endpoints' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Async Endpoints</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      For complex multi-role tasks, RouKey provides dedicated async endpoints to handle long-running operations without timeout limits.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Submit Async Job</h2>
                    <p className="text-gray-600 mb-4">
                      Submit a request for asynchronous processing. Ideal for complex multi-role tasks that may take several minutes.
                    </p>
                    <CodeBlock title="POST /api/external/v1/async/submit" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/async/submit" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Create a comprehensive business plan for a tech startup"}
    ],
    "temperature": 0.7,
    "webhook_url": "https://your-app.com/webhook/roukey"
  }'`}
                    </CodeBlock>

                    <h3 className="text-lg font-semibold text-gray-900 mt-6 mb-4">Request Parameters</h3>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Parameter</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Type</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Required</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Description</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-4 py-3 text-sm font-mono text-[#ff6b35]">messages</td>
                            <td className="px-4 py-3 text-sm text-gray-600">array</td>
                            <td className="px-4 py-3 text-sm text-green-600">Yes</td>
                            <td className="px-4 py-3 text-sm text-gray-600">Array of message objects</td>
                          </tr>
                          <tr>
                            <td className="px-4 py-3 text-sm font-mono text-[#ff6b35]">webhook_url</td>
                            <td className="px-4 py-3 text-sm text-gray-600">string</td>
                            <td className="px-4 py-3 text-sm text-gray-600">No</td>
                            <td className="px-4 py-3 text-sm text-gray-600">URL to receive completion notifications</td>
                          </tr>
                          <tr>
                            <td className="px-4 py-3 text-sm font-mono text-[#ff6b35]">role</td>
                            <td className="px-4 py-3 text-sm text-gray-600">string</td>
                            <td className="px-4 py-3 text-sm text-gray-600">No</td>
                            <td className="px-4 py-3 text-sm text-gray-600">Custom role for routing</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mt-6 mb-4">Response</h3>
                    <CodeBlock title="Async submit response" language="json">
{`{
  "job_id": "job_abc123def456",
  "status": "pending",
  "estimated_completion": "2024-01-15T10:35:00Z",
  "created_at": "2024-01-15T10:30:00Z",
  "progress_percentage": 0,
  "polling_url": "https://roukey.online/api/external/v1/async/status/job_abc123def456",
  "result_url": "https://roukey.online/api/external/v1/async/result/job_abc123def456",
  "webhook_configured": true,
  "message": "Job submitted for async processing. Use polling_url to check status or configure webhook_url for notifications."
}`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Check Job Status</h2>
                    <p className="text-gray-600 mb-4">
                      Poll the status of an async job to track progress and completion.
                    </p>
                    <CodeBlock title="GET /api/external/v1/async/status/{jobId}" language="bash">
{`curl -X GET "https://roukey.online/api/external/v1/async/status/job_abc123def456" \\
  -H "X-API-Key: rk_live_your_api_key_here"`}
                    </CodeBlock>

                    <h3 className="text-lg font-semibold text-gray-900 mt-6 mb-4">Response</h3>
                    <CodeBlock title="Status response" language="json">
{`{
  "job_id": "job_abc123def456",
  "status": "processing",
  "progress_percentage": 65,
  "created_at": "2024-01-15T10:30:00Z",
  "started_at": "2024-01-15T10:30:15Z",
  "estimated_completion": "2024-01-15T10:35:00Z",
  "estimated_remaining_minutes": 2,
  "elapsed_minutes": 3,
  "roles_detected": ["business_analyst", "market_researcher"],
  "webhook_configured": true,
  "result_available": false,
  "result_url": null
}`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Get Job Result</h2>
                    <p className="text-gray-600 mb-4">
                      Retrieve the completed result of an async job.
                    </p>
                    <CodeBlock title="GET /api/external/v1/async/result/{jobId}" language="bash">
{`curl -X GET "https://roukey.online/api/external/v1/async/result/job_abc123def456" \\
  -H "X-API-Key: rk_live_your_api_key_here"`}
                    </CodeBlock>

                    <h3 className="text-lg font-semibold text-gray-900 mt-6 mb-4">Response</h3>
                    <CodeBlock title="Result response" language="json">
{`{
  "id": "chatcmpl-abc123",
  "object": "chat.completion",
  "created": **********,
  "model": "routed-model",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "# Comprehensive Business Plan\\n\\n## Executive Summary\\n..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 45,
    "completion_tokens": 2847,
    "total_tokens": 2892
  },
  "rokey_metadata": {
    "job_id": "job_abc123def456",
    "roles_used": ["business_analyst", "market_researcher"],
    "total_processing_time_ms": 245000,
    "routing_strategy": "intelligent_role"
  }
}`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Job Status Values</h2>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">pending</span>
                        <span className="text-gray-600">Job is queued and waiting to start</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">processing</span>
                        <span className="text-gray-600">Job is actively being processed</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">completed</span>
                        <span className="text-gray-600">Job finished successfully, result available</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">failed</span>
                        <span className="text-gray-600">Job failed, check error_message</span>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Webhook Notifications:</strong> Configure a webhook_url when submitting jobs to receive automatic notifications when processing completes, eliminating the need for polling.
                  </Alert>
                </div>
              )}

              {activeSection === 'api-reference-image-support' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Image Support</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey supports multimodal content including images, allowing you to send visual content alongside text for analysis, description, and processing.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Supported Image Formats</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <PhotoIcon className="h-5 w-5 text-blue-600" />
                          Image Formats
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• JPEG (.jpg, .jpeg)</li>
                          <li>• PNG (.png)</li>
                          <li>• WebP (.webp)</li>
                          <li>• GIF (.gif)</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CheckIcon className="h-5 w-5 text-green-600" />
                          Input Methods
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Base64 encoded images</li>
                          <li>• Public image URLs</li>
                          <li>• Data URLs (data:image/...)</li>
                          <li>• Multiple images per message</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Using Public Image URLs</h2>
                    <p className="text-gray-600 mb-4">
                      The simplest way to include images is by referencing publicly accessible image URLs.
                    </p>
                    <CodeBlock title="Image URL example" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What do you see in this image? Describe it in detail."
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/path/to/your/image.jpg"
            }
          }
        ]
      }
    ]
  }'`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Using Base64 Encoded Images</h2>
                    <p className="text-gray-600 mb-4">
                      For private images or when you want to send image data directly, use base64 encoding.
                    </p>
                    <CodeBlock title="Base64 image example" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "Analyze this chart and explain the trends."
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
            }
          }
        ]
      }
    ]
  }'`}
                    </CodeBlock>

                    <Alert type="tip">
                      <strong>Base64 Encoding:</strong> You can convert images to base64 using command line tools like <code className="bg-gray-100 px-2 py-1 rounded">base64 image.jpg</code> or online converters.
                    </Alert>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Multiple Images</h2>
                    <p className="text-gray-600 mb-4">
                      You can include multiple images in a single message for comparison, analysis, or batch processing.
                    </p>
                    <CodeBlock title="Multiple images example" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "Compare these two images and tell me the differences."
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image1.jpg"
            }
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image2.jpg"
            }
          }
        ]
      }
    ]
  }'`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Image Analysis Use Cases</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Visual Analysis</h3>
                        <ul className="space-y-2 text-gray-600 text-sm">
                          <li>• Object detection and identification</li>
                          <li>• Scene description and analysis</li>
                          <li>• Text extraction (OCR)</li>
                          <li>• Color and composition analysis</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-orange-50 rounded-xl border border-orange-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Business Applications</h3>
                        <ul className="space-y-2 text-gray-600 text-sm">
                          <li>• Document processing</li>
                          <li>• Chart and graph analysis</li>
                          <li>• Product catalog descriptions</li>
                          <li>• Quality control inspection</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Creative Tasks</h3>
                        <ul className="space-y-2 text-gray-600 text-sm">
                          <li>• Image captioning</li>
                          <li>• Style and mood analysis</li>
                          <li>• Creative writing prompts</li>
                          <li>• Art and design feedback</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Technical Analysis</h3>
                        <ul className="space-y-2 text-gray-600 text-sm">
                          <li>• Code screenshot analysis</li>
                          <li>• Diagram interpretation</li>
                          <li>• Technical documentation</li>
                          <li>• Error message debugging</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Vision Model Compatibility</h2>
                    <p className="text-gray-600 mb-4">
                      When sending images, ensure your configured models support vision capabilities. RouKey will pass multimodal content to your selected models based on your routing strategy.
                    </p>
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl border border-blue-200">
                      <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <SparklesIcon className="h-5 w-5 text-blue-600" />
                        Popular Vision-Capable Models
                      </h3>
                      <ul className="space-y-2 text-gray-600">
                        <li>• <strong>GPT-4 Vision:</strong> Excellent for detailed analysis and complex reasoning</li>
                        <li>• <strong>Claude Vision:</strong> Great for document analysis and text extraction</li>
                        <li>• <strong>Gemini Vision:</strong> Fast processing for simple image tasks</li>
                        <li>• <strong>Model Configuration:</strong> Ensure your API keys are configured with vision-capable models</li>
                      </ul>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Best Practices</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CheckIcon className="h-5 w-5 text-green-600" />
                          Optimization Tips
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Use clear, high-resolution images for better analysis</li>
                          <li>• Provide specific instructions about what to analyze</li>
                          <li>• For multiple images, explain the relationship between them</li>
                          <li>• Use public URLs when possible to reduce payload size</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                          Limitations
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Maximum image size: 20MB per image</li>
                          <li>• Base64 images increase request size significantly</li>
                          <li>• Some models may have different vision capabilities</li>
                          <li>• Processing time increases with image complexity</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <Alert type="info">
                    <strong>Model Configuration:</strong> Make sure your configured API keys include vision-capable models when working with images. RouKey will route requests according to your selected routing strategy.
                  </Alert>
                </div>
              )}

              {activeSection === 'api-reference-parameters' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Parameters</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Complete reference for all supported parameters in RouKey API requests.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Common Parameters</h2>
                    <div className="space-y-6">
                      <div className="border-l-4 border-[#ff6b35] pl-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">messages</h3>
                        <p className="text-gray-600 mb-2">Array of message objects that make up the conversation.</p>
                        <CodeBlock title="Message format" language="json">
{`{
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello!"},
    {"role": "assistant", "content": "Hi there! How can I help you?"},
    {"role": "user", "content": "What's the weather like?"}
  ]
}`}
                        </CodeBlock>
                      </div>

                      <div className="border-l-4 border-blue-500 pl-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">stream</h3>
                        <p className="text-gray-600 mb-2">Boolean flag to enable streaming responses (default: false).</p>
                        <CodeBlock title="Streaming enabled" language="json">
{`{
  "messages": [...],
  "stream": true
}`}
                        </CodeBlock>
                      </div>

                      <div className="border-l-4 border-green-500 pl-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">max_tokens</h3>
                        <p className="text-gray-600 mb-2">Maximum number of tokens to generate in the response.</p>
                        <CodeBlock title="Token limit" language="json">
{`{
  "messages": [...],
  "max_tokens": 1000
}`}
                        </CodeBlock>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'api-reference-responses' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Responses</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Understanding RouKey's response formats for both streaming and non-streaming requests.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Standard Response</h2>
                    <p className="text-gray-600 mb-4">
                      RouKey returns OpenAI-compatible responses with additional metadata about routing and processing.
                    </p>
                    <CodeBlock title="Non-streaming response format" language="json">
{`{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": **********,
  "model": "routed-model",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm here to help you with any questions you have."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 12,
    "completion_tokens": 15,
    "total_tokens": 27
  },
  "rokey_metadata": {
    "routing_strategy": "complexity_based",
    "selected_provider": "openai",
    "selected_model": "gpt-4",
    "processing_time_ms": 1250,
    "roles_detected": ["general"],
    "complexity_score": 0.3
  }
}`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">RouKey Metadata</h2>
                    <p className="text-gray-600 mb-4">
                      Every response includes RouKey-specific metadata to help you understand how your request was processed.
                    </p>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Field</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Type</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Description</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">routing_strategy</td>
                            <td className="px-6 py-4 text-sm text-gray-600">string</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Strategy used for routing (e.g., "complexity_based", "intelligent_role")</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">selected_provider</td>
                            <td className="px-6 py-4 text-sm text-gray-600">string</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Provider that handled the request</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">selected_model</td>
                            <td className="px-6 py-4 text-sm text-gray-600">string</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Specific model used for generation</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">processing_time_ms</td>
                            <td className="px-6 py-4 text-sm text-gray-600">number</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Total processing time in milliseconds</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">roles_detected</td>
                            <td className="px-6 py-4 text-sm text-gray-600">array</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Roles detected in the request for multi-role routing</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">complexity_score</td>
                            <td className="px-6 py-4 text-sm text-gray-600">number</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Complexity score (0.0-1.0) used for routing decisions</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Streaming Response</h2>
                    <p className="text-gray-600 mb-4">
                      Streaming responses are sent as Server-Sent Events with incremental content:
                    </p>
                    <CodeBlock title="Streaming response chunks" language="json">
{`data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"role":"assistant"},"index":0,"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"content":"Hello"},"index":0,"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"routed-model","choices":[{"delta":{"content":"!"},"index":0,"finish_reason":"stop"}]}

data: [DONE]`}
                    </CodeBlock>
                  </div>
                </div>
              )}

              {activeSection === 'api-reference-errors' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Error Handling</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey uses standard HTTP status codes and provides detailed error messages for troubleshooting.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Error Response Format</h2>
                    <p className="text-gray-600 mb-4">
                      All RouKey API errors follow a consistent JSON structure with detailed error information.
                    </p>
                    <CodeBlock title="Error response structure" language="json">
{`{
  "error": {
    "message": "API key is required. Provide it in Authorization header as \\"Bearer YOUR_API_KEY\\" or in x-api-key header.",
    "type": "authentication_error",
    "code": "invalid_api_key"
  }
}`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">HTTP Status Codes</h2>
                    <div className="space-y-4">
                      <div className="p-4 bg-red-50 rounded-xl border border-red-200">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="bg-red-600 text-white px-2 py-1 rounded text-sm font-medium">401</span>
                          <span className="font-semibold text-gray-900">Unauthorized</span>
                        </div>
                        <p className="text-gray-600 text-sm mb-2">Invalid, missing, or expired API key</p>
                        <div className="text-xs text-gray-500">
                          <strong>Error type:</strong> authentication_error<br/>
                          <strong>Common codes:</strong> invalid_api_key, expired_api_key
                        </div>
                      </div>
                      <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="bg-yellow-600 text-white px-2 py-1 rounded text-sm font-medium">403</span>
                          <span className="font-semibold text-gray-900">Forbidden</span>
                        </div>
                        <p className="text-gray-600 text-sm mb-2">API key doesn't have required permissions or IP restrictions</p>
                        <div className="text-xs text-gray-500">
                          <strong>Error type:</strong> permission_denied<br/>
                          <strong>Common codes:</strong> insufficient_permissions, ip_not_allowed
                        </div>
                      </div>
                      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="bg-blue-600 text-white px-2 py-1 rounded text-sm font-medium">400</span>
                          <span className="font-semibold text-gray-900">Bad Request</span>
                        </div>
                        <p className="text-gray-600 text-sm mb-2">Invalid request parameters or malformed JSON</p>
                        <div className="text-xs text-gray-500">
                          <strong>Error type:</strong> api_error<br/>
                          <strong>Common codes:</strong> invalid_request, validation_error
                        </div>
                      </div>
                      <div className="p-4 bg-orange-50 rounded-xl border border-orange-200">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="bg-orange-600 text-white px-2 py-1 rounded text-sm font-medium">404</span>
                          <span className="font-semibold text-gray-900">Not Found</span>
                        </div>
                        <p className="text-gray-600 text-sm mb-2">Requested resource (e.g., async job) not found</p>
                        <div className="text-xs text-gray-500">
                          <strong>Error type:</strong> not_found_error<br/>
                          <strong>Common codes:</strong> job_not_found, resource_not_found
                        </div>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="bg-purple-600 text-white px-2 py-1 rounded text-sm font-medium">500</span>
                          <span className="font-semibold text-gray-900">Internal Server Error</span>
                        </div>
                        <p className="text-gray-600 text-sm mb-2">Unexpected server error - please try again</p>
                        <div className="text-xs text-gray-500">
                          <strong>Error type:</strong> internal_error<br/>
                          <strong>Common codes:</strong> server_error, internal_error
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Error Examples</h2>
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Authentication Error</h3>
                        <CodeBlock title="Missing API key" language="json">
{`{
  "error": {
    "message": "API key is required. Provide it in Authorization header as \\"Bearer YOUR_API_KEY\\" or in x-api-key header.",
    "type": "authentication_error",
    "code": "invalid_api_key"
  }
}`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Permission Error</h3>
                        <CodeBlock title="Insufficient permissions" language="json">
{`{
  "error": {
    "message": "API key does not have chat permission",
    "type": "permission_error",
    "code": "insufficient_permissions"
  }
}`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Validation Error</h3>
                        <CodeBlock title="Invalid request data" language="json">
{`{
  "error": {
    "message": "Messages array cannot be empty and must contain at least one message.",
    "type": "api_error",
    "code": "validation_error"
  }
}`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Async Job Not Found</h3>
                        <CodeBlock title="Job not found" language="json">
{`{
  "error": {
    "message": "Job not found",
    "type": "not_found_error",
    "code": "job_not_found"
  }
}`}
                        </CodeBlock>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Routing Strategies Subsections */}
              {activeSection === 'routing-strategies-strategy-overview' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Strategy Overview</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey offers six distinct routing strategies, each designed for specific use cases and optimization goals.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Available Strategies</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Default Load Balancing</h3>
                        <p className="text-gray-600 text-sm mb-3">Automatic distribution with zero configuration</p>
                        <div className="text-xs text-blue-600 font-medium">Strategy: none</div>
                      </div>
                      <div className="p-6 bg-[#ff6b35]/10 rounded-xl border border-[#ff6b35]/20">
                        <h3 className="font-semibold text-gray-900 mb-3">Intelligent Role Routing</h3>
                        <p className="text-gray-600 text-sm mb-3">AI-powered role classification and routing</p>
                        <div className="text-xs text-[#ff6b35] font-medium">Strategy: intelligent_role</div>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Complexity-Based Routing</h3>
                        <p className="text-gray-600 text-sm mb-3">Cost optimization through complexity analysis</p>
                        <div className="text-xs text-green-600 font-medium">Strategy: complexity_round_robin</div>
                      </div>
                      <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Cost-Optimized Routing</h3>
                        <p className="text-gray-600 text-sm mb-3">Learning-based cost optimization</p>
                        <div className="text-xs text-purple-600 font-medium">Strategy: cost_optimized</div>
                      </div>
                      <div className="p-6 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Strict Fallback</h3>
                        <p className="text-gray-600 text-sm mb-3">Predictable ordered failover sequence</p>
                        <div className="text-xs text-yellow-600 font-medium">Strategy: strict_fallback</div>
                      </div>
                      <div className="p-6 bg-indigo-50 rounded-xl border border-indigo-200">
                        <h3 className="font-semibold text-gray-900 mb-3">A/B Testing</h3>
                        <p className="text-gray-600 text-sm mb-3">Split traffic for performance comparison</p>
                        <div className="text-xs text-indigo-600 font-medium">Strategy: ab_routing</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'routing-strategies-intelligent-role-routing' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Intelligent Role Routing Setup</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Set up AI-powered role classification through the RouKey app interface to automatically route requests to specialized models.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Step-by-Step Setup</h2>
                    <div className="space-y-8">

                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold">1</div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">Create Custom Roles</h3>
                          <p className="text-gray-600 mb-4">
                            Navigate to your model configuration page and create custom roles for different task types.
                          </p>
                          <div className="bg-gray-50 p-4 rounded-xl border border-gray-200">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="text-center">
                                <div className="w-12 h-12 bg-blue-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                  <CodeBracketIcon className="w-6 h-6 text-white" />
                                </div>
                                <p className="text-sm font-medium text-gray-900">coding</p>
                                <p className="text-xs text-gray-600">Programming tasks</p>
                              </div>
                              <div className="text-center">
                                <div className="w-12 h-12 bg-green-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                  <DocumentTextIcon className="w-6 h-6 text-white" />
                                </div>
                                <p className="text-sm font-medium text-gray-900">writing</p>
                                <p className="text-xs text-gray-600">Content creation</p>
                              </div>
                              <div className="text-center">
                                <div className="w-12 h-12 bg-purple-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                  <ChartBarIcon className="w-6 h-6 text-white" />
                                </div>
                                <p className="text-sm font-medium text-gray-900">analysis</p>
                                <p className="text-xs text-gray-600">Data analysis</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold">2</div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">Assign Models to Roles</h3>
                          <p className="text-gray-600 mb-4">
                            For each API key in your configuration, assign it to the appropriate roles based on the model's strengths.
                          </p>
                          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
                                <div className="flex items-center gap-3">
                                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span className="text-xs font-bold text-blue-600">DS</span>
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">DeepSeek Coder</p>
                                    <p className="text-xs text-gray-600">Specialized for coding tasks</p>
                                  </div>
                                </div>
                                <div className="flex gap-2">
                                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">coding</span>
                                </div>
                              </div>
                              <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
                                <div className="flex items-center gap-3">
                                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <span className="text-xs font-bold text-purple-600">C3</span>
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">Claude 3.5 Sonnet</p>
                                    <p className="text-xs text-gray-600">Excellent for writing and analysis</p>
                                  </div>
                                </div>
                                <div className="flex gap-2">
                                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">writing</span>
                                  <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">analysis</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold">3</div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">Enable Intelligent Role Routing</h3>
                          <p className="text-gray-600 mb-4">
                            Go to Advanced Routing Setup and select "Intelligent Role Routing" as your strategy.
                          </p>
                          <div className="bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-xl border border-orange-200">
                            <div className="flex items-center gap-3 mb-3">
                              <BoltIcon className="w-6 h-6 text-orange-600" />
                              <h4 className="font-medium text-gray-900">Routing Strategy Selection</h4>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">
                              Navigate to: <strong>Model Configuration → Advanced Routing Setup → Intelligent Role Routing</strong>
                            </p>
                            <div className="bg-white p-3 rounded-lg border border-orange-100">
                              <p className="text-xs text-gray-700">
                                ✅ RouKey will automatically classify user prompts and route to the best model for each role
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold">4</div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">Set Default Fallback Model</h3>
                          <p className="text-gray-600 mb-4">
                            Configure a default general chat model for requests that don't match any specific role.
                          </p>
                          <div className="bg-gray-50 p-4 rounded-xl border border-gray-200">
                            <div className="flex items-center gap-3 mb-2">
                              <ShieldCheckIcon className="w-5 h-5 text-gray-600" />
                              <span className="text-sm font-medium text-gray-900">Fallback Protection</span>
                            </div>
                            <p className="text-xs text-gray-600">
                              If no role matches the user's request, RouKey will automatically use your designated default model to ensure every request gets a response.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Role Management Tips</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <LightBulbIcon className="w-5 h-5 text-blue-600" />
                          Best Practices
                        </h3>
                        <ul className="space-y-2 text-gray-600 text-sm">
                          <li>• Use descriptive role IDs (coding, writing, analysis)</li>
                          <li>• Assign models based on their strengths</li>
                          <li>• Test different role assignments to optimize performance</li>
                          <li>• Keep role descriptions clear and specific</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CheckIcon className="w-5 h-5 text-green-600" />
                          Common Role Types
                        </h3>
                        <ul className="space-y-2 text-gray-600 text-sm">
                          <li>• <strong>coding:</strong> Programming, debugging, code review</li>
                          <li>• <strong>writing:</strong> Content creation, editing, copywriting</li>
                          <li>• <strong>analysis:</strong> Data analysis, research, insights</li>
                          <li>• <strong>creative:</strong> Brainstorming, design, ideation</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Pro Tip:</strong> Start with 2-3 basic roles (coding, writing, general) and expand as you identify specific use cases. RouKey's AI classification becomes more accurate with well-defined role assignments.
                  </Alert>
                </div>
              )}

              {activeSection === 'routing-strategies-complexity-routing' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Complexity-Based Routing</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Optimize costs by routing simple requests to cheaper models and complex requests to more capable models.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Complexity Analysis</h2>
                    <p className="text-gray-600 mb-6">
                      RouKey analyzes each request and assigns a complexity score from 1-5, then routes to the appropriate model tier:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                      <div className="text-center p-4 bg-green-50 rounded-xl border border-green-200">
                        <div className="text-2xl font-bold text-green-600 mb-2">1</div>
                        <div className="text-sm font-medium text-gray-900 mb-1">Simple</div>
                        <div className="text-xs text-gray-600">Basic Q&A</div>
                      </div>
                      <div className="text-center p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <div className="text-2xl font-bold text-blue-600 mb-2">2</div>
                        <div className="text-sm font-medium text-gray-900 mb-1">Easy</div>
                        <div className="text-xs text-gray-600">Explanations</div>
                      </div>
                      <div className="text-center p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <div className="text-2xl font-bold text-yellow-600 mb-2">3</div>
                        <div className="text-sm font-medium text-gray-900 mb-1">Medium</div>
                        <div className="text-xs text-gray-600">Analysis</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-xl border border-orange-200">
                        <div className="text-2xl font-bold text-orange-600 mb-2">4</div>
                        <div className="text-sm font-medium text-gray-900 mb-1">Hard</div>
                        <div className="text-xs text-gray-600">Complex tasks</div>
                      </div>
                      <div className="text-center p-4 bg-red-50 rounded-xl border border-red-200">
                        <div className="text-2xl font-bold text-red-600 mb-2">5</div>
                        <div className="text-sm font-medium text-gray-900 mb-1">Expert</div>
                        <div className="text-xs text-gray-600">Advanced reasoning</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'routing-strategies-cost-optimized' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Cost-Optimized Routing</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Machine learning-based routing that learns from usage patterns to minimize costs while maintaining quality.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Learning Algorithm</h2>
                    <p className="text-gray-600 mb-6">
                      This strategy continuously learns from your usage patterns and optimizes routing decisions based on:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Cost Analysis</h3>
                        <p className="text-gray-600 text-sm">Tracks actual costs per request type and optimizes for minimum spend</p>
                      </div>
                      <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Quality Metrics</h3>
                        <p className="text-gray-600 text-sm">Monitors response quality to ensure cost savings don't compromise results</p>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Usage Patterns</h3>
                        <p className="text-gray-600 text-sm">Learns from your specific use cases and request patterns</p>
                      </div>
                      <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Performance Data</h3>
                        <p className="text-gray-600 text-sm">Considers response time and success rates in routing decisions</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'routing-strategies-strict-fallback' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Strict Fallback</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Predictable ordered failover sequence that tries API keys in a specific order for maximum reliability.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
                    <div className="space-y-4">
                      <div className="flex items-center gap-4 p-4 bg-green-50 rounded-xl border border-green-200">
                        <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                        <div>
                          <h3 className="font-semibold text-gray-900">Primary API Key</h3>
                          <p className="text-gray-600 text-sm">Always tries the first configured API key first</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <div className="w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                        <div>
                          <h3 className="font-semibold text-gray-900">Secondary Fallback</h3>
                          <p className="text-gray-600 text-sm">If primary fails, tries the second API key</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                        <div>
                          <h3 className="font-semibold text-gray-900">Continue Sequence</h3>
                          <p className="text-gray-600 text-sm">Continues through all configured keys in order</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'routing-strategies-ab-testing' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">A/B Testing</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Split traffic between different models to compare performance, cost, and quality metrics.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Traffic Splitting</h2>
                    <p className="text-gray-600 mb-6">
                      Configure percentage-based traffic distribution between different API keys or models:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Group A (70%)</h3>
                        <p className="text-gray-600 text-sm mb-2">Primary model for comparison</p>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{width: '70%'}}></div>
                        </div>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Group B (30%)</h3>
                        <p className="text-gray-600 text-sm mb-2">Alternative model being tested</p>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-green-600 h-2 rounded-full" style={{width: '30%'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Add placeholder content for other sections */}
              {activeSection === 'routing-strategies' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Routing Strategies</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey's intelligent routing strategies are the core of cost optimization and performance enhancement.
                      Each strategy uses advanced AI classification and analysis to route requests to the optimal model based on your specific requirements.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Strategy Overview</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg leading-relaxed">
                        RouKey offers seven distinct routing strategies, each designed for specific use cases and optimization goals.
                        All strategies include automatic failover, retry logic, and comprehensive analytics. The choice of strategy
                        depends on your priorities: cost optimization, reliability, performance, or specialized routing requirements.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Default Load Balancing</h3>
                          <p className="text-gray-600 text-sm">Automatic distribution with zero configuration</p>
                        </div>
                        <div className="p-4 bg-[#ff6b35]/10 rounded-xl border border-[#ff6b35]/20">
                          <h3 className="font-semibold text-gray-900 mb-2">Intelligent Role Routing</h3>
                          <p className="text-gray-600 text-sm">AI-powered role classification and routing</p>
                        </div>
                        <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Complexity-Based Routing</h3>
                          <p className="text-gray-600 text-sm">Cost optimization through complexity analysis</p>
                        </div>
                        <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Cost-Optimized Routing</h3>
                          <p className="text-gray-600 text-sm">Learning-based cost optimization</p>
                        </div>
                        <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Strict Fallback</h3>
                          <p className="text-gray-600 text-sm">Predictable ordered failover sequence</p>
                        </div>
                        <div className="p-4 bg-indigo-50 rounded-xl border border-indigo-200">
                          <h3 className="font-semibold text-gray-900 mb-2">A/B Testing</h3>
                          <p className="text-gray-600 text-sm">Split traffic for performance comparison</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Default Load Balancing</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200">
                        <div className="flex items-center gap-3 mb-4">
                          <CogIcon className="h-6 w-6 text-blue-600" />
                          <h3 className="text-xl font-semibold text-gray-900">Zero Configuration Required</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          The default strategy requires no setup and automatically distributes requests across all active API keys
                          in your configuration. Perfect for getting started quickly or when you want simple, reliable load balancing.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">How It Works</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Round-Robin Distribution:</strong> Requests are distributed evenly across all available API keys</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Automatic Retry:</strong> Failed requests are automatically retried with different keys</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Health Monitoring:</strong> Unhealthy keys are temporarily removed from rotation</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Load Balancing:</strong> Prevents any single API key from being overwhelmed</span>
                            </li>
                          </ul>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Best Use Cases</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Getting started with RouKey</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Simple applications with consistent workloads</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>High-availability requirements</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>When you want zero configuration overhead</span>
                            </li>
                          </ul>
                        </div>
                      </div>

                      <Alert type="tip">
                        <strong>Performance:</strong> This strategy provides excellent reliability with 99.9% uptime and automatic failover.
                        While it doesn't optimize for cost like other strategies, it ensures consistent performance and is perfect for production workloads.
                      </Alert>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Intelligent Role Routing</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20">
                        <div className="flex items-center gap-3 mb-4">
                          <BoltIcon className="h-6 w-6 text-[#ff6b35]" />
                          <h3 className="text-xl font-semibold text-gray-900">AI-Powered Request Classification</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          RouKey's most sophisticated routing strategy uses advanced AI classification to analyze each request and
                          determine the optimal model based on the detected role or task type. This enables specialized routing
                          for different types of work like coding, writing, analysis, and more.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Supported Roles</h3>
                          <div className="space-y-3">
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">general_chat</div>
                              <div className="text-sm text-gray-600">General conversation and Q&A</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">coding</div>
                              <div className="text-sm text-gray-600">Code generation, debugging, and review</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">writing</div>
                              <div className="text-sm text-gray-600">Content creation and editing</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">analysis</div>
                              <div className="text-sm text-gray-600">Data analysis and research</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">creative</div>
                              <div className="text-sm text-gray-600">Creative writing and brainstorming</div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">How Classification Works</h3>
                          <div className="space-y-4">
                            <div className="flex items-start gap-3">
                              <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0">
                                <span className="text-white font-bold text-sm">1</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Request Analysis</div>
                                <div className="text-sm text-gray-600">RouKey's AI classifier analyzes the user's prompt to understand intent and context</div>
                              </div>
                            </div>
                            <div className="flex items-start gap-3">
                              <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0">
                                <span className="text-white font-bold text-sm">2</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Role Detection</div>
                                <div className="text-sm text-gray-600">The system identifies the most appropriate role category for the request</div>
                              </div>
                            </div>
                            <div className="flex items-start gap-3">
                              <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0">
                                <span className="text-white font-bold text-sm">3</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Model Selection</div>
                                <div className="text-sm text-gray-600">Routes to the API key configured for that specific role</div>
                              </div>
                            </div>
                            <div className="flex items-start gap-3">
                              <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0">
                                <span className="text-white font-bold text-sm">4</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Fallback Handling</div>
                                <div className="text-sm text-gray-600">If no specific role match, uses the default general chat model</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Multi-Role Detection & Orchestration</h3>
                        <p className="text-gray-600 mb-4">
                          For complex requests requiring multiple specialized capabilities, RouKey can detect multi-role requirements
                          and orchestrate workflows using our advanced RouKey integration.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Sequential Workflow</div>
                            <div className="text-sm text-gray-600">Single roles, step-by-step</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Supervisor Workflow</div>
                            <div className="text-sm text-gray-600">2-3 roles, coordinated</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Hierarchical Workflow</div>
                            <div className="text-sm text-gray-600">4+ roles, complex tasks</div>
                          </div>
                        </div>
                      </div>

                      <Alert type="info">
                        <strong>Streaming Recommended:</strong> For multi-role tasks, enable streaming to avoid timeouts and get real-time progress updates.
                        RouKey automatically detects complex multi-role requirements and processes them asynchronously with streaming support.
                      </Alert>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Complexity-Based Routing</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
                        <div className="flex items-center gap-3 mb-4">
                          <CircleStackIcon className="h-6 w-6 text-green-600" />
                          <h3 className="text-xl font-semibold text-gray-900">Cost Optimization Through Intelligence</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          RouKey's complexity-based routing analyzes each prompt to determine its complexity level (1-5 scale) and
                          routes it to the most cost-effective model capable of handling that complexity. This strategy can reduce
                          costs by up to 60% while maintaining response quality.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Complexity Levels</h3>
                          <div className="space-y-3">
                            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">1</div>
                              <div>
                                <div className="font-medium text-gray-900">Simple</div>
                                <div className="text-sm text-gray-600">Basic Q&A, simple requests</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">2</div>
                              <div>
                                <div className="font-medium text-gray-900">Basic</div>
                                <div className="text-sm text-gray-600">Explanations, summaries</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                              <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">3</div>
                              <div>
                                <div className="font-medium text-gray-900">Moderate</div>
                                <div className="text-sm text-gray-600">Analysis, code review</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                              <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">4</div>
                              <div>
                                <div className="font-medium text-gray-900">Complex</div>
                                <div className="text-sm text-gray-600">Advanced reasoning, complex code</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                              <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">5</div>
                              <div>
                                <div className="font-medium text-gray-900">Expert</div>
                                <div className="text-sm text-gray-600">Research, advanced problem-solving</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Routing Logic</h3>
                          <div className="space-y-4">
                            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-2">Exact Match Routing</h4>
                              <p className="text-gray-600 text-sm">
                                RouKey first attempts to route to API keys specifically configured for the detected complexity level.
                              </p>
                            </div>
                            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-2">Proximal Search</h4>
                              <p className="text-gray-600 text-sm">
                                If no exact match is found, RouKey searches adjacent complexity levels (±1) to find suitable models.
                              </p>
                            </div>
                            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-2">Round-Robin Distribution</h4>
                              <p className="text-gray-600 text-sm">
                                Multiple keys at the same complexity level are used in round-robin fashion for load balancing.
                              </p>
                            </div>
                            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-2">Fallback Protection</h4>
                              <p className="text-gray-600 text-sm">
                                If no suitable complexity match is found, falls back to default general chat model.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Cost Optimization Example</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Before RouKey</h4>
                            <p className="text-gray-600 text-sm mb-3">All requests go to GPT-4 ($0.03/1K tokens)</p>
                            <ul className="text-gray-600 text-sm space-y-1">
                              <li>• Simple Q&A: $0.03/1K tokens</li>
                              <li>• Basic explanations: $0.03/1K tokens</li>
                              <li>• Complex analysis: $0.03/1K tokens</li>
                              <li>• <strong>Average cost: $0.03/1K tokens</strong></li>
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">With Complexity Routing</h4>
                            <p className="text-gray-600 text-sm mb-3">Intelligent model selection based on complexity</p>
                            <ul className="text-gray-600 text-sm space-y-1">
                              <li>• Simple Q&A: GPT o3 ($0.001/1K tokens)</li>
                              <li>• Basic explanations: GPT o3 ($0.001/1K tokens)</li>
                              <li>• Complex analysis: GPT-4 ($0.03/1K tokens)</li>
                              <li>• <strong>Average cost: $0.012/1K tokens (60% savings)</strong></li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <Alert type="tip">
                        <strong>Best Practice:</strong> Configure cheaper models (GPT o3, Claude Haiku) for complexity levels 1-2,
                        mid-tier models for level 3, and premium models (GPT-4, Claude Opus) for levels 4-5 to maximize cost savings.
                      </Alert>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Cost-Optimized Routing</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200">
                        <div className="flex items-center gap-3 mb-4">
                          <SparklesIcon className="h-6 w-6 text-purple-600" />
                          <h3 className="text-xl font-semibold text-gray-900">Learning-Based Optimization</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          RouKey's most advanced cost optimization strategy that learns from your usage patterns over time.
                          It combines complexity analysis with user behavior learning to provide personalized cost optimization
                          while maintaining response quality standards.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Learning Phases</h3>
                          <div className="space-y-4">
                            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                              <h4 className="font-medium text-gray-900 mb-2">Phase 1: Learning (First 50 requests)</h4>
                              <p className="text-gray-600 text-sm">
                                Conservative approach using complexity-based routing while collecting usage data and user feedback patterns.
                              </p>
                            </div>
                            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                              <h4 className="font-medium text-gray-900 mb-2">Phase 2: Optimization (51+ requests)</h4>
                              <p className="text-gray-600 text-sm">
                                Advanced routing using learned patterns, cost profiles, and quality preferences for maximum optimization.
                              </p>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Optimization Factors</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Usage Patterns:</strong> Analyzes your typical request types and complexity distribution</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Cost Sensitivity:</strong> Learns your cost vs. quality preferences from routing decisions</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Performance Metrics:</strong> Tracks response quality and user satisfaction indicators</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Model Performance:</strong> Monitors which models perform best for your specific use cases</span>
                            </li>
                          </ul>
                        </div>
                      </div>

                      <div className="bg-green-50 p-6 rounded-xl border border-green-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Advanced Features</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Adaptive Learning</div>
                            <div className="text-sm text-gray-600">Continuously improves routing decisions based on outcomes</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Quality Monitoring</div>
                            <div className="text-sm text-gray-600">Ensures cost optimization doesn't compromise response quality</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Budget Awareness</div>
                            <div className="text-sm text-gray-600">Considers your budget constraints in routing decisions</div>
                          </div>
                        </div>
                      </div>

                      <Alert type="warning">
                        <strong>Learning Period:</strong> This strategy requires a learning period of 50+ requests to reach optimal performance.
                        During the learning phase, it uses conservative complexity-based routing while building your usage profile.
                      </Alert>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Strict Fallback Strategy</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 p-6 rounded-xl border border-yellow-200">
                        <div className="flex items-center gap-3 mb-4">
                          <ListBulletIcon className="h-6 w-6 text-yellow-600" />
                          <h3 className="text-xl font-semibold text-gray-900">Predictable Ordered Routing</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          The strict fallback strategy provides maximum reliability and predictability by defining an ordered list
                          of API keys. RouKey will try them in sequence until one succeeds, making it perfect for mission-critical
                          applications where predictable behavior is essential.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">How It Works</h3>
                          <div className="space-y-3">
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">1</div>
                              <div>
                                <div className="font-medium text-gray-900">Primary Model</div>
                                <div className="text-sm text-gray-600">First choice, highest priority</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">2</div>
                              <div>
                                <div className="font-medium text-gray-900">Secondary Model</div>
                                <div className="text-sm text-gray-600">Backup if primary fails</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">3</div>
                              <div>
                                <div className="font-medium text-gray-900">Tertiary Model</div>
                                <div className="text-sm text-gray-600">Final fallback option</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                              <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">✗</div>
                              <div>
                                <div className="font-medium text-gray-900">Error Response</div>
                                <div className="text-sm text-gray-600">If all models fail</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Configuration Example</h3>
                          <CodeBlock title="Fallback order configuration" language="json">
{`{
  "routing_strategy": "strict_fallback",
  "routing_strategy_params": {
    "fallback_order": [
      "primary-gpt4-key",
      "backup-claude-key",
      "emergency-gpt35-key"
    ],
    "retry_attempts": 3,
    "timeout_seconds": 30
  }
}`}
                          </CodeBlock>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Best Use Cases</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Mission-critical applications requiring guaranteed responses</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Applications with strict SLA requirements</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>When you need predictable routing behavior</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Compliance requirements for specific model usage</span>
                            </li>
                          </ul>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Advanced Features</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Health Monitoring:</strong> Automatically skips unhealthy models in the chain</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Retry Logic:</strong> Configurable retry attempts for each model in the chain</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Timeout Control:</strong> Individual timeout settings for each fallback level</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Detailed Logging:</strong> Complete audit trail of fallback decisions</span>
                            </li>
                          </ul>
                        </div>
                      </div>

                      <Alert type="tip">
                        <strong>Reliability:</strong> This strategy provides the highest reliability with guaranteed fallback behavior.
                        It's ideal for production environments where consistent availability is more important than cost optimization.
                      </Alert>
                    </div>
                  </div>
                </div>
              )}

              {/* Configuration Subsections */}
              {activeSection === 'configuration-dashboard-setup' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Dashboard Setup</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Get started with RouKey by setting up your dashboard and creating your first configuration.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Step-by-Step Setup</h2>
                    <div className="space-y-6">
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">Create Your Account</h3>
                          <p className="text-gray-600">Sign up at <a href="https://roukey.online" className="text-[#ff6b35] hover:underline">roukey.online</a> with your email address. Email verification is required.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">Access Your Dashboard</h3>
                          <p className="text-gray-600">After logging in, you'll see your main dashboard with options to create configurations, manage API keys, and view analytics.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">Create Your First Configuration</h3>
                          <p className="text-gray-600">Click "Create Configuration" to set up your first routing configuration. Give it a descriptive name like "Production API" or "Development Setup".</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">4</div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">Add Provider API Keys</h3>
                          <p className="text-gray-600">Add your LLM provider API keys (OpenAI, Anthropic, Google, etc.) to enable routing to different models.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">5</div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">Generate User API Key</h3>
                          <p className="text-gray-600">Create a user-generated API key that you'll use to make requests to RouKey from your applications.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Dashboard Overview</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Configurations</h3>
                        <p className="text-gray-600 text-sm">Manage your routing configurations, each with its own set of API keys and routing strategy.</p>
                      </div>
                      <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Analytics</h3>
                        <p className="text-gray-600 text-sm">View usage statistics, cost analysis, and performance metrics for your API calls.</p>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-2">User API Keys</h3>
                        <p className="text-gray-600 text-sm">Generate and manage API keys for external access to your RouKey configurations.</p>
                      </div>
                      <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Custom Roles</h3>
                        <p className="text-gray-600 text-sm">Create specialized roles for intelligent routing based on your specific use cases.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'configuration-provider-keys' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Provider API Keys</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Learn how to add and manage API keys from different LLM providers in RouKey.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Supported Providers</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-2">OpenAI</h3>
                        <p className="text-gray-600 text-sm mb-2">GPT-4, GPT o3, GPT-4 Turbo</p>
                        <div className="text-xs text-green-600 font-medium">Most Popular</div>
                      </div>
                      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Anthropic</h3>
                        <p className="text-gray-600 text-sm mb-2">Claude 4 Opus, Claude 3 Haiku</p>
                        <div className="text-xs text-blue-600 font-medium">High Quality</div>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Google</h3>
                        <p className="text-gray-600 text-sm mb-2">Gemini 2.5 Pro, Gemini Flash</p>
                        <div className="text-xs text-purple-600 font-medium">Cost Effective</div>
                      </div>
                      <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-2">DeepSeek</h3>
                        <p className="text-gray-600 text-sm mb-2">Deepseek R1 0528, DeepSeek Chat</p>
                        <div className="text-xs text-yellow-600 font-medium">Coding Specialist</div>
                      </div>
                      <div className="p-4 bg-indigo-50 rounded-xl border border-indigo-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Mistral</h3>
                        <p className="text-gray-600 text-sm mb-2">Mistral Large, Mistral Medium</p>
                        <div className="text-xs text-indigo-600 font-medium">European</div>
                      </div>
                      <div className="p-4 bg-teal-50 rounded-xl border border-teal-200">
                        <h3 className="font-semibold text-gray-900 mb-2">xAI</h3>
                        <p className="text-gray-600 text-sm mb-2">Grok Models</p>
                        <div className="text-xs text-teal-600 font-medium">Advanced</div>
                      </div>
                      <div className="p-4 bg-red-50 rounded-xl border border-red-200">
                        <h3 className="font-semibold text-gray-900 mb-2">And More</h3>
                        <p className="text-gray-600 text-sm mb-2">300+ models supported</p>
                        <div className="text-xs text-red-600 font-medium">Growing</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Adding API Keys</h2>
                    <div className="space-y-6">
                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Security First</h3>
                        <p className="text-gray-600 mb-3">
                          All API keys are encrypted at rest using AES-256 encryption. RouKey never stores your keys in plain text.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Keys are encrypted before database storage</li>
                          <li>• Decryption only happens during API calls</li>
                          <li>• Keys are never logged or exposed in responses</li>
                          <li>• Regular security audits and compliance checks</li>
                        </ul>
                      </div>

                      <div className="bg-green-50 p-6 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Best Practices</h3>
                        <ul className="text-gray-600 space-y-2">
                          <li>• Use descriptive labels for your API keys (e.g., "OpenAI Production", "Claude Backup")</li>
                          <li>• Add multiple keys from the same provider for load balancing</li>
                          <li>• Set appropriate temperature settings for each key</li>
                          <li>• Regularly rotate your provider API keys for security</li>
                          <li>• Monitor usage through your provider dashboards</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'configuration' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Configuration</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Learn how to configure RouKey for optimal performance with your specific use cases.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Dashboard Setup</h3>
                      <p className="text-gray-600 mb-4">Get started with your RouKey dashboard and create your first configuration.</p>
                      <div className="text-[#ff6b35] font-medium">→ Dashboard Setup Guide</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Provider API Keys</h3>
                      <p className="text-gray-600 mb-4">Add and manage API keys from different LLM providers.</p>
                      <div className="text-[#ff6b35] font-medium">→ Provider Keys Guide</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Routing Configuration</h3>
                      <p className="text-gray-600 mb-4">Set up intelligent routing strategies for your use case.</p>
                      <div className="text-[#ff6b35] font-medium">→ Routing Setup Guide</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Custom Roles</h3>
                      <p className="text-gray-600 mb-4">Create specialized roles for intelligent routing.</p>
                      <div className="text-[#ff6b35] font-medium">→ Custom Roles Guide</div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'configuration-routing-config' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Routing Configuration</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Configure intelligent routing strategies to optimize cost, performance, and reliability.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Choosing a Routing Strategy</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Default Load Balancing (none)</h3>
                        <p className="text-gray-600 mb-3">Perfect for getting started. Automatically distributes requests across all your API keys.</p>
                        <div className="text-sm text-blue-600 font-medium">✓ Zero configuration • ✓ High reliability • ✓ Automatic failover</div>
                      </div>
                      <div className="p-6 bg-[#ff6b35]/10 rounded-xl border border-[#ff6b35]/20">
                        <h3 className="font-semibold text-gray-900 mb-3">Intelligent Role Routing (intelligent_role)</h3>
                        <p className="text-gray-600 mb-3">AI classifies requests and routes to specialized models. Requires custom roles setup.</p>
                        <div className="text-sm text-[#ff6b35] font-medium">✓ AI-powered • ✓ Specialized routing • ✓ Cost optimization</div>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Complexity-Based Routing (complexity_round_robin)</h3>
                        <p className="text-gray-600 mb-3">Routes simple requests to cheaper models, complex ones to premium models.</p>
                        <div className="text-sm text-green-600 font-medium">✓ Cost optimization • ✓ Automatic complexity analysis • ✓ Smart routing</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Configuration Steps</h2>
                    <div className="space-y-4">
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">Select Your Configuration</h3>
                          <p className="text-gray-600">Go to your configuration and click on "Routing Setup" to access routing options.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">Choose Strategy</h3>
                          <p className="text-gray-600">Select the routing strategy that best fits your use case and requirements.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">Configure Parameters</h3>
                          <p className="text-gray-600">Set up strategy-specific parameters like role assignments or complexity thresholds.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold text-sm">4</div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-2">Test & Monitor</h3>
                          <p className="text-gray-600">Test your configuration and monitor performance through the analytics dashboard.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'configuration-custom-roles' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Custom Roles</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Create specialized roles to optimize routing for your specific use cases and requirements.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">What are Custom Roles?</h2>
                    <p className="text-gray-600 mb-6">
                      Custom roles allow you to define specialized routing behavior for different types of requests.
                      When using intelligent role routing, RouKey's AI classifies incoming requests and routes them
                      to the most appropriate model based on your custom role definitions.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Coding Role</h3>
                        <p className="text-gray-600 text-sm mb-2">Optimized for programming tasks</p>
                        <div className="text-xs text-blue-600">Primary: Claude 4 Opus • Fallback: GPT-4</div>
                      </div>
                      <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Writing Role</h3>
                        <p className="text-gray-600 text-sm mb-2">Specialized for content creation</p>
                        <div className="text-xs text-green-600">Primary: Claude 4 Opus • Fallback: GPT-4</div>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Analysis Role</h3>
                        <p className="text-gray-600 text-sm mb-2">For data analysis and research</p>
                        <div className="text-xs text-purple-600">Primary: GPT-4 • Fallback: Claude 4 Opus</div>
                      </div>
                      <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-2">General Chat</h3>
                        <p className="text-gray-600 text-sm mb-2">Default for unclassified requests</p>
                        <div className="text-xs text-yellow-600">Primary: Gemini 2.5 Pro • Fallback: GPT o3</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Creating Custom Roles</h2>
                    <div className="space-y-6">
                      <div className="bg-green-50 p-6 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Role Configuration</h3>
                        <p className="text-gray-600 mb-4">Each custom role requires:</p>
                        <ul className="text-gray-600 space-y-2">
                          <li>• <strong>Role Name:</strong> Descriptive identifier (e.g., "coding", "writing", "analysis")</li>
                          <li>• <strong>Primary Model:</strong> The preferred API key/model for this role</li>
                          <li>• <strong>Fallback Models:</strong> Backup options if primary fails</li>
                          <li>• <strong>Description:</strong> Clear description of when this role should be used</li>
                        </ul>
                      </div>

                      <Alert type="info">
                        <strong>Tier Requirements:</strong> Custom roles are available on Starter tier and above.
                        Free tier users can only use the default load balancing strategy.
                      </Alert>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'configuration-temperature-settings' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Temperature Settings</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Fine-tune response creativity and consistency by configuring temperature settings for your API keys.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Understanding Temperature</h2>
                    <p className="text-gray-600 mb-6">
                      Temperature controls the randomness of the model's responses. Lower values make responses more
                      focused and deterministic, while higher values increase creativity and variability.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <div className="text-3xl font-bold text-blue-600 mb-2">0.0 - 0.3</div>
                        <div className="font-semibold text-gray-900 mb-2">Conservative</div>
                        <div className="text-sm text-gray-600">Focused, deterministic responses. Best for factual questions, code generation, and analysis.</div>
                      </div>
                      <div className="text-center p-6 bg-green-50 rounded-xl border border-green-200">
                        <div className="text-3xl font-bold text-green-600 mb-2">0.4 - 0.7</div>
                        <div className="font-semibold text-gray-900 mb-2">Balanced</div>
                        <div className="text-sm text-gray-600">Good balance of creativity and consistency. Ideal for most general-purpose applications.</div>
                      </div>
                      <div className="text-center p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <div className="text-3xl font-bold text-purple-600 mb-2">0.8 - 2.0</div>
                        <div className="font-semibold text-gray-900 mb-2">Creative</div>
                        <div className="text-sm text-gray-600">High creativity and variability. Perfect for creative writing, brainstorming, and diverse outputs.</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Use Case Examples</h2>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-50 rounded-xl">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-semibold text-gray-900">Code Generation</h3>
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">Temperature: 0.1</span>
                        </div>
                        <p className="text-gray-600 text-sm">Low temperature ensures consistent, syntactically correct code with minimal randomness.</p>
                      </div>
                      <div className="p-4 bg-gray-50 rounded-xl">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-semibold text-gray-900">Customer Support</h3>
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Temperature: 0.5</span>
                        </div>
                        <p className="text-gray-600 text-sm">Balanced temperature provides helpful responses while maintaining consistency in tone.</p>
                      </div>
                      <div className="p-4 bg-gray-50 rounded-xl">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-semibold text-gray-900">Creative Writing</h3>
                          <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">Temperature: 0.9</span>
                        </div>
                        <p className="text-gray-600 text-sm">Higher temperature encourages creative, diverse, and imaginative responses.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'sdks' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">SDKs & Libraries</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Official SDKs and community libraries for RouKey.
                    </p>
                  </div>
                  <div className="card p-8 text-center">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Coming Soon</h3>
                    <p className="text-gray-600">SDK documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'limits' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Limits & Pricing</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Understanding RouKey's usage limits and pricing structure.
                    </p>
                  </div>
                  <div className="card p-8 text-center">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Coming Soon</h3>
                    <p className="text-gray-600">Limits and pricing documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {/* Future Releases Subsections */}
              {activeSection === 'future-releases-q1-2025' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Q1 2025 - Workflow Automation</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Advanced workflow automation capabilities for complex multi-step AI tasks and orchestration.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Planned Features</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Hierarchical Workflow Engine</h3>
                        <p className="text-gray-600 mb-3">
                          Advanced workflow orchestration similar to n8n, but specifically designed for AI tasks with memory and context preservation.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Visual workflow builder with drag-and-drop interface</li>
                          <li>• Memory persistence across workflow steps</li>
                          <li>• Conditional branching and loops</li>
                          <li>• Integration with external APIs and databases</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Multi-Agent Orchestration</h3>
                        <p className="text-gray-600 mb-3">
                          Coordinate multiple AI agents working together on complex tasks with role-based specialization.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Agent role definitions and capabilities</li>
                          <li>• Inter-agent communication protocols</li>
                          <li>• Task delegation and result aggregation</li>
                          <li>• Conflict resolution and consensus mechanisms</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Workflow Templates</h3>
                        <p className="text-gray-600 mb-3">
                          Pre-built workflow templates for common use cases like research, content creation, and data analysis.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Research and literature review workflows</li>
                          <li>• Content creation and editing pipelines</li>
                          <li>• Data analysis and reporting workflows</li>
                          <li>• Custom template creation and sharing</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Expected Benefits</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Productivity Boost</h3>
                        <p className="text-gray-600 text-sm">Automate complex multi-step tasks that currently require manual coordination.</p>
                      </div>
                      <div className="p-4 bg-red-50 rounded-xl border border-red-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Cost Efficiency</h3>
                        <p className="text-gray-600 text-sm">Optimize model usage across workflow steps for maximum cost savings.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'future-releases-q2-2025' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Q2 2025 - Performance & Scale</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Enhanced performance optimizations and enterprise-scale features for high-volume applications.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Performance Enhancements</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Advanced Caching Layer</h3>
                        <p className="text-gray-600 mb-3">
                          Multi-tier caching system with semantic similarity matching and intelligent cache invalidation.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Semantic cache with vector similarity matching</li>
                          <li>• Distributed cache across multiple regions</li>
                          <li>• Smart cache warming and preloading</li>
                          <li>• Cache analytics and optimization recommendations</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Edge Computing</h3>
                        <p className="text-gray-600 mb-3">
                          Deploy RouKey routing logic closer to your users for reduced latency and improved performance.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Global edge network deployment</li>
                          <li>• Regional model routing optimization</li>
                          <li>• Latency-based provider selection</li>
                          <li>• Edge caching for frequently used responses</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Enterprise Scale Features</h2>
                    <div className="space-y-4">
                      <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Multi-Tenant Architecture</h3>
                        <p className="text-gray-600 text-sm">Complete isolation and resource management for enterprise customers.</p>
                      </div>
                      <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Advanced Analytics</h3>
                        <p className="text-gray-600 text-sm">Real-time performance monitoring and predictive analytics.</p>
                      </div>
                      <div className="p-4 bg-red-50 rounded-xl border border-red-200">
                        <h3 className="font-semibold text-gray-900 mb-2">Auto-Scaling</h3>
                        <p className="text-gray-600 text-sm">Automatic resource scaling based on usage patterns and demand.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'future-releases-q3-2025' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">Q3 2025 - AI-Powered Features</h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Next-generation AI features including fine-tuning, custom model training, and advanced optimization.
                    </p>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">AI Enhancement Features</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-[#ff6b35]/10 rounded-xl border border-[#ff6b35]/20">
                        <h3 className="font-semibold text-gray-900 mb-3">Custom Model Fine-Tuning</h3>
                        <p className="text-gray-600 mb-3">
                          Fine-tune models on your specific data and use cases for improved performance and specialized capabilities.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Upload and process training datasets</li>
                          <li>• Automated fine-tuning pipeline</li>
                          <li>• Model performance evaluation and comparison</li>
                          <li>• Integration with existing routing strategies</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-indigo-50 rounded-xl border border-indigo-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Intelligent Model Selection</h3>
                        <p className="text-gray-600 mb-3">
                          AI-powered model selection that learns from your usage patterns and automatically optimizes routing decisions.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Machine learning-based routing optimization</li>
                          <li>• Continuous learning from user feedback</li>
                          <li>• Predictive model performance scoring</li>
                          <li>• Automatic A/B testing of routing strategies</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'future-releases-q4-2025' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Q4 2025 - Enterprise Features</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Advanced enterprise features including on-premises deployment, advanced security, and compliance tools.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Enterprise Deployment Options</h2>
                    <div className="space-y-6">
                      <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                        <h3 className="font-semibold text-gray-900 mb-3">On-Premises Deployment</h3>
                        <p className="text-gray-600 mb-3">
                          Deploy RouKey within your own infrastructure for maximum security and control.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Self-hosted RouKey instances</li>
                          <li>• Air-gapped deployment options</li>
                          <li>• Custom security configurations</li>
                          <li>• Integration with existing enterprise systems</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Advanced Security & Compliance</h3>
                        <p className="text-gray-600 mb-3">
                          Enterprise-grade security features and compliance certifications for regulated industries.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• SOC 2 Type II certification</li>
                          <li>• HIPAA compliance for healthcare</li>
                          <li>• GDPR compliance for EU operations</li>
                          <li>• Advanced audit logging and monitoring</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'future-releases' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">
                      Future Releases
                    </h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Discover what's coming next in RouKey's roadmap and planned features for upcoming releases.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Q1 2025 - Workflow Automation</h3>
                      <p className="text-gray-600 mb-4">Advanced workflow automation and multi-agent orchestration capabilities.</p>
                      <div className="text-[#ff6b35] font-medium">→ Q1 2025 Details</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Q2 2025 - Performance & Scale</h3>
                      <p className="text-gray-600 mb-4">Enhanced performance optimizations and enterprise-scale features.</p>
                      <div className="text-[#ff6b35] font-medium">→ Q2 2025 Details</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Q3 2025 - AI-Powered Features</h3>
                      <p className="text-gray-600 mb-4">Custom model fine-tuning and intelligent optimization features.</p>
                      <div className="text-[#ff6b35] font-medium">→ Q3 2025 Details</div>
                    </div>
                    <div className="card p-4">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Q4 2025 - Enterprise Features</h3>
                      <p className="text-gray-600 mb-4">On-premises deployment and advanced enterprise security features.</p>
                      <div className="text-[#ff6b35] font-medium">→ Q4 2025 Details</div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Q1 2025 - Enhanced Workflow Automation</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <RocketLaunchIcon className="h-5 w-5 text-blue-600" />
                          Advanced Workflow Engine
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Full n8n-style workflow automation with visual workflow builder and advanced task orchestration.
                        </p>
                        <div className="text-sm text-blue-600 font-medium">Status: In Development</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <SparklesIcon className="h-5 w-5 text-green-600" />
                          Enhanced Memory System
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Persistent memory across workflow executions with intelligent context management and sub-task tracking.
                        </p>
                        <div className="text-sm text-green-600 font-medium">Status: Design Phase</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Q2 2025 - Performance & Scale</h2>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Ultra-Low Latency</h3>
                          <p className="text-gray-600 mb-4">Target latency reduction to 50-100ms range for lightning-fast responses.</p>
                          <div className="text-sm text-gray-500">Current: 100-500ms</div>
                        </div>
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Global Edge Network</h3>
                          <p className="text-gray-600 mb-4">Worldwide edge deployment for reduced latency and improved reliability.</p>
                          <div className="text-sm text-gray-500">Planned: 15+ regions</div>
                        </div>
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Advanced Caching</h3>
                          <p className="text-gray-600 mb-4">Multi-layer caching with predictive pre-loading and intelligent invalidation.</p>
                          <div className="text-sm text-gray-500">Target: 90% cache hit rate</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Q3 2025 - AI-Powered Features</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Intelligent Cost Prediction</h3>
                        <p className="text-gray-600 mb-4">
                          AI-powered cost forecasting and budget optimization with predictive analytics.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Monthly cost predictions</li>
                          <li>• Usage pattern analysis</li>
                          <li>• Automatic budget alerts</li>
                          <li>• Optimization recommendations</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Auto-Scaling Routing</h3>
                        <p className="text-gray-600 mb-4">
                          Dynamic routing strategies that adapt to traffic patterns and model performance in real-time.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Traffic-based scaling</li>
                          <li>• Performance monitoring</li>
                          <li>• Automatic strategy switching</li>
                          <li>• Load balancing optimization</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-4">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Q4 2025 - Enterprise Features</h2>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Advanced Analytics Dashboard</h3>
                          <p className="text-gray-600 mb-4">
                            Comprehensive analytics with custom reporting, cost breakdowns, and performance insights.
                          </p>
                          <ul className="space-y-1 text-gray-600 text-sm">
                            <li>• Custom dashboards</li>
                            <li>• Real-time monitoring</li>
                            <li>• Cost attribution</li>
                            <li>• Performance metrics</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                          <h3 className="font-semibold text-gray-900 mb-3">Enterprise Security</h3>
                          <p className="text-gray-600 mb-4">
                            Advanced security features including SSO, audit logs, and compliance certifications.
                          </p>
                          <ul className="space-y-1 text-gray-600 text-sm">
                            <li>• Single Sign-On (SSO)</li>
                            <li>• Audit logging</li>
                            <li>• SOC 2 compliance</li>
                            <li>• Role-based access control</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-6 rounded-xl text-white">
                    <h3 className="text-lg font-semibold mb-3">Want to influence our roadmap?</h3>
                    <p className="text-white/90 mb-4">
                      We value feedback from our community. Share your feature requests and help shape RouKey's future.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <a
                        href="mailto:<EMAIL>"
                        className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors text-center"
                      >
                        Submit Feature Request
                      </a>
                      <a
                        href="https://github.com/DRIM-ai/RouKey"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors text-center"
                      >
                        View on GitHub
                      </a>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'faq' && (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">
                      Frequently Asked Questions
                    </h1>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      Find answers to common questions about RouKey's features, pricing, and implementation.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">How does RouKey reduce API costs?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        RouKey reduces costs through intelligent routing strategies that automatically select the most cost-effective model for each request.
                        Our complexity-based routing can route simple tasks to cheaper models (like GPT o3) while reserving premium models (like GPT-4) for complex tasks.
                        Additionally, our semantic caching system prevents duplicate API calls for similar requests, further reducing costs.
                      </p>
                    </div>

                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">Is RouKey compatible with existing OpenAI code?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Yes! RouKey is fully compatible with OpenAI's API. You can use existing OpenAI SDKs and simply change the base URL to RouKey's endpoint.
                        All OpenAI parameters are supported, plus RouKey adds additional parameters like "role" for enhanced routing capabilities.
                      </p>
                    </div>

                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">What happens if a model is unavailable?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        RouKey includes automatic failover mechanisms. If your primary model is unavailable, RouKey will automatically route to backup models
                        based on your configured strategy. Our strict fallback strategy provides guaranteed failover chains, while other strategies include
                        intelligent fallback logic to ensure high availability.
                      </p>
                    </div>

                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">How secure are my API keys?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Your API keys are encrypted using enterprise-grade AES-256 encryption and stored securely in our database.
                        RouKey follows a BYOK (Bring Your Own Keys) model, meaning you maintain control of your API keys.
                        We never store or log your actual API responses, and all communications are encrypted in transit.
                      </p>
                    </div>

                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">Can I use RouKey for production applications?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Absolutely! RouKey is designed for production use with enterprise-grade reliability, security, and performance.
                        We offer 99.9% uptime SLA, comprehensive monitoring, and 24/7 support for professional and enterprise plans.
                        Many companies use RouKey to handle millions of requests per month.
                      </p>
                    </div>

                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">What's the difference between routing strategies?</h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        RouKey offers several routing strategies for different use cases:
                      </p>
                      <ul className="space-y-2 text-gray-600 ml-4">
                        <li>• <strong>Intelligent Role:</strong> AI classifies requests by role (coding, writing, etc.) and routes accordingly</li>
                        <li>• <strong>Complexity-Based:</strong> Analyzes prompt complexity and routes to appropriate models for cost optimization</li>
                        <li>• <strong>Strict Fallback:</strong> Ordered failover sequence for maximum reliability</li>
                        <li>• <strong>Cost-Optimized:</strong> Learns your usage patterns and optimizes for cost while maintaining quality</li>
                      </ul>
                    </div>

                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">Do you support streaming responses?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Yes! RouKey fully supports streaming responses and we actually recommend using streaming for complex multi-role tasks.
                        Streaming helps avoid timeout issues on platforms like Vercel and provides a better user experience with real-time responses.
                        Our multi-role orchestration system works seamlessly with streaming.
                      </p>
                    </div>

                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">How does the free tier work?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        The free tier includes unlimited API requests, 1 custom configuration, up to 3 API keys, access to all 300+ models,
                        and strict fallback routing. However, it doesn't include advanced routing strategies, custom roles, or premium features.
                        It's perfect for testing RouKey and small projects.
                      </p>
                    </div>

                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">Can I cancel my subscription anytime?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Yes, you can cancel your subscription at any time. Your service will continue until the end of your current billing period,
                        and you can always reactivate later. We also offer a 14-day money-back guarantee for new subscribers.
                      </p>
                    </div>

                    <div className="card p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">How do I get support?</h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        We offer multiple support channels:
                      </p>
                      <ul className="space-y-2 text-gray-600 ml-4">
                        <li>• <strong>Email:</strong> <EMAIL> for general inquiries</li>
                        <li>• <strong>Technical:</strong> <EMAIL> for technical support</li>
                        <li>• <strong>Documentation:</strong> Comprehensive guides and examples</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-6 rounded-xl text-white">
                    <h3 className="text-lg font-semibold mb-3">Still have questions?</h3>
                    <p className="text-white/90 mb-4">
                      Can't find what you're looking for? Our team is here to help with any questions about RouKey.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <a
                        href="mailto:<EMAIL>"
                        className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors text-center"
                      >
                        Contact Support
                      </a>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
