'use client';

import React from 'react';
import { useSubscription } from '@/hooks/useSubscription';
import { hasFeatureAccess, getTierConfig, SubscriptionTier } from '@/lib/stripe-client';
import { UpgradePrompt } from './UpgradePrompt';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface TierGuardProps {
  feature: 'custom_roles' | 'knowledge_base' | 'advanced_routing' | 'prompt_engineering' | 'semantic_caching' | 'configurations';
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  customMessage?: string;
  currentCount?: number; // For features that have count-based limits (like configurations)
}

/**
 * TierGuard component that conditionally renders content based on user's subscription tier
 * Shows upgrade prompt if user doesn't have access to the feature
 */
export function TierGuard({
  feature,
  children,
  fallback,
  showUpgradePrompt = true,
  customMessage,
  currentCount = 0
}: TierGuardProps) {
  const { subscriptionStatus, loading } = useSubscription();

  if (loading) {
    return <LoadingSpinner />;
  }

  const userTier = subscriptionStatus?.tier || 'free';

  // Special handling for configurations feature - check count-based limits
  if (feature === 'configurations') {
    const tierConfig = getTierConfig(userTier as SubscriptionTier);
    const hasAccess = currentCount < tierConfig.limits.configurations;

    if (hasAccess) {
      return <>{children}</>;
    }
  } else {
    // For other features, use the standard feature access check
    const hasAccess = hasFeatureAccess(userTier as SubscriptionTier, feature);

    if (hasAccess) {
      return <>{children}</>;
    }
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showUpgradePrompt) {
    return (
      <UpgradePrompt 
        feature={feature}
        currentTier={userTier as SubscriptionTier}
        customMessage={customMessage}
      />
    );
  }

  return null;
}
